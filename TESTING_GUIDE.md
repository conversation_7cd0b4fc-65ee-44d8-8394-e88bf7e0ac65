# Vertex AI Batch Job Monitoring - Testing Guide

This guide provides step-by-step instructions for testing the Vertex AI batch job monitoring system using the exact scripts and commands that were used during development and verification.

## 🧪 Complete Testing Workflow

### Step 1: System Setup and Clean Start

```bash
# 1. Stop the system and clean all data
cd /home/<USER>/LongevityCo
docker-compose -f docker-compose.vm.yml down -v

# 2. Start fresh system
docker-compose -f docker-compose.vm.yml up -d

# 3. Wait for services to be ready (30-60 seconds)
sleep 30

# 4. Verify all containers are running
docker ps
```

**Expected Output:** All containers (postgres, neo4j, redis, api, worker) should show "Up" status.

### Step 2: Verify Clean Database State

```bash
# Check PostgreSQL is empty
docker exec longevityco-postgres-1 psql -U longevity -d longevity -c "
SELECT COUNT(*) as documents FROM documents; 
SELECT COUNT(*) as tasks FROM processing_tasks; 
SELECT COUNT(*) as entities FROM entities;"

# Check Neo4j is empty
docker exec longevityco-neo4j-1 cypher-shell -u neo4j -p MKvA0gTsdmjVv9NwAPKyK2OpVw6nAEJl "
MATCH (n) RETURN count(n) as total_nodes;"
```

**Expected Output:** All counts should be 0.

### Step 3: Upload Test Document

```bash
# Create a test document (or use existing file)
cat > test_document.txt << 'EOF'
Alzheimer's disease is a progressive neurodegenerative disorder that affects memory, thinking, and behavior. The disease is characterized by the accumulation of amyloid-beta plaques and tau protein tangles in the brain. These pathological changes lead to neuronal death and brain atrophy. Current treatments include cholinesterase inhibitors like donepezil and memantine, which can help manage symptoms but do not cure the disease. Research is ongoing into new therapeutic approaches, including immunotherapy targeting amyloid-beta and tau proteins.

Parkinson's disease is another neurodegenerative disorder that primarily affects movement. It is characterized by the loss of dopamine-producing neurons in the substantia nigra. The main symptoms include tremor, rigidity, bradykinesia, and postural instability. Treatment typically involves dopamine replacement therapy with levodopa, dopamine agonists, and in some cases, deep brain stimulation.

Multiple sclerosis is an autoimmune disease that affects the central nervous system. The immune system attacks the myelin sheath that protects nerve fibers, leading to inflammation and scarring. This results in a wide range of symptoms including fatigue, difficulty walking, numbness, and cognitive problems. Treatment focuses on disease-modifying therapies to slow progression and manage symptoms.
EOF

# Upload document with async knowledge graph building
curl -X POST "http://localhost:8000/api/documents/ingest" \
  -F "file=@test_document.txt" \
  -F "title=Neurodegenerative Diseases Overview" \
  -F "metadata_str={\"source\": \"test\", \"async_kg_building\": true}"
```

**Expected Output:** JSON response with document ID and success status.

### Step 4: Monitor Processing in Real-Time

```bash
# Start monitoring worker logs (run in separate terminal)
docker logs longevityco-worker-1 --tail=20 -f
```

**What to Look For:**
- Document processing starts
- KG chunks creation
- Vertex AI batch job submission
- Batch job monitoring every minute
- Batch job completion detection
- Automatic KG building continuation

### Step 5: Check Task Progression

```bash
# Check task statuses (run every 1-2 minutes)
docker exec longevityco-postgres-1 psql -U longevity -d longevity -c "
SELECT id, task_type, status, created_at, result->>'batch_job_id' as batch_job_id 
FROM processing_tasks 
ORDER BY created_at DESC;"
```

**Expected Progression:**
1. `document_processing` tasks appear and complete
2. `knowledge_graph_building` task appears in `processing` status
3. Task moves to `batch_job_submitted` with batch_job_id
4. `vertex_ai_batch_job` task appears in `pending` status
5. Both tasks move to `completed` status

### Step 6: Verify Entity Extraction

```bash
# Check entity extraction progress
docker exec longevityco-postgres-1 psql -U longevity -d longevity -c "
SELECT 
  'Documents' as type, COUNT(*) as count FROM documents
UNION ALL
SELECT 
  'Chunks' as type, COUNT(*) as count FROM chunks
UNION ALL
SELECT 
  'KG Chunks' as type, COUNT(*) as count FROM kg_chunks
UNION ALL
SELECT 
  'Entities' as type, COUNT(*) as count FROM entities;"
```

**Expected Output:** 
- Documents: 1
- Chunks: 10-20 (varies by document size)
- KG Chunks: 3-5
- Entities: 10-30 (varies by content)

### Step 7: Verify Knowledge Graph Creation

```bash
# Check Neo4j nodes
docker exec longevityco-neo4j-1 cypher-shell -u neo4j -p MKvA0gTsdmjVv9NwAPKyK2OpVw6nAEJl "
MATCH (n) RETURN labels(n) as node_types, count(n) as count ORDER BY count DESC;"

# Check Neo4j relationships
docker exec longevityco-neo4j-1 cypher-shell -u neo4j -p MKvA0gTsdmjVv9NwAPKyK2OpVw6nAEJl "
MATCH ()-[r]->() RETURN type(r) as relationship_types, count(r) as count ORDER BY count DESC;"
```

**Expected Output:**
- Entity nodes: 10-30
- Chunk nodes: 3-5
- Document nodes: 1
- Various relationship types (MENTIONED_IN, RELATED_TO, PART_OF, etc.)

### Step 8: Test Multiple Documents

```bash
# Upload second document
curl -X POST "http://localhost:8000/api/documents/ingest" \
  -F "file=@test_document.txt" \
  -F "title=Second Test Document" \
  -F "metadata_str={\"source\": \"test2\", \"async_kg_building\": true}"

# Monitor concurrent processing
docker logs longevityco-worker-1 --tail=30 | grep -E "(batch_job|BATCH JOB)"
```

**Expected Behavior:** System should handle multiple documents concurrently.

## 🔍 Monitoring and Debugging Scripts

### Real-Time Monitoring

```bash
# Monitor batch job checks specifically
docker logs longevityco-worker-1 --tail=50 -f | grep "BATCH JOB CHECK"

# Monitor task processing
docker logs longevityco-worker-1 --tail=50 -f | grep -E "(knowledge_graph|vertex_ai)"

# Monitor errors
docker logs longevityco-worker-1 --tail=50 -f | grep -E "(ERROR|WARNING)"
```

### Manual Monitoring Trigger

```bash
# Manually trigger batch job monitoring
docker exec longevityco-worker-1 python -c "
from services.knowledge_graph_service import KnowledgeGraphService
service = KnowledgeGraphService()
result = service.check_and_continue_batch_jobs()
print(f'Result: {result}')
"
```

### Task Status Analysis

```bash
# Get detailed task information
docker exec longevityco-postgres-1 psql -U longevity -d longevity -c "
SELECT 
  task_type,
  status,
  COUNT(*) as count,
  MIN(created_at) as oldest,
  MAX(created_at) as newest
FROM processing_tasks 
GROUP BY task_type, status 
ORDER BY task_type, status;"
```

### Celery Worker Status

```bash
# Check active Celery tasks
docker exec longevityco-worker-1 celery -A workers.celery_app inspect active

# Check scheduled tasks
docker exec longevityco-worker-1 celery -A workers.celery_app inspect scheduled

# Check worker stats
docker exec longevityco-worker-1 celery -A workers.celery_app inspect stats
```

## ✅ Success Criteria

### 1. Task Flow Success
- [ ] Document upload returns success
- [ ] Document processing completes
- [ ] KG building task created with `async_processing: true`
- [ ] Vertex AI batch job submitted (task gets `batch_job_id`)
- [ ] Monitoring detects completed batch job
- [ ] Both KG and vertex_ai_batch_job tasks marked as `completed`

### 2. Data Extraction Success
- [ ] Entities extracted and stored in PostgreSQL
- [ ] Knowledge graph nodes created in Neo4j
- [ ] Relationships created between entities
- [ ] Chunk-to-document relationships established

### 3. Monitoring System Success
- [ ] Scheduled monitoring runs every minute
- [ ] Logs show "BATCH JOB CHECK STARTED/COMPLETED"
- [ ] Completed jobs detected within 1 minute
- [ ] Automatic continuation of KG building
- [ ] No tasks stuck in intermediate states

## 🚨 Troubleshooting

### Common Issues and Solutions

**Issue: Tasks stuck in `batch_job_submitted`**
```bash
# Check Vertex AI batch job status manually
docker exec longevityco-worker-1 python -c "
from transport.vertex_ai_batch_client import VertexAIBatchClient
client = VertexAIBatchClient()
# Replace with actual batch job ID from database
job_id = 'projects/261769042093/locations/europe-west4/batchPredictionJobs/YOUR_JOB_ID'
state = client.get_batch_job_state(job_id)
print(f'Job state: {state}')
"
```

**Issue: No entities extracted**
```bash
# Check if batch results were downloaded
docker exec longevityco-postgres-1 psql -U longevity -d longevity -c "
SELECT result FROM processing_tasks 
WHERE task_type = 'knowledge_graph_building' 
AND result ? 'batch_results';"
```

**Issue: Monitoring not running**
```bash
# Check if Celery Beat is scheduling tasks
docker logs longevityco-worker-1 | grep "check-vertex-ai-batch-jobs"

# Restart worker if needed
docker-compose -f docker-compose.vm.yml restart worker
```

## 🧹 Cleanup

```bash
# Remove test files
rm -f test_document.txt

# Stop system (optional)
docker-compose -f docker-compose.vm.yml down

# Complete cleanup including data (optional)
docker-compose -f docker-compose.vm.yml down -v
```

## 📊 Expected Timeline

- **Document Upload**: < 30 seconds
- **Document Processing**: 1-3 minutes
- **Batch Job Submission**: 1-2 minutes
- **Batch Job Processing**: 2-5 minutes (Vertex AI)
- **Monitoring Detection**: < 1 minute after completion
- **KG Building Continuation**: 1-2 minutes
- **Total End-to-End**: 5-15 minutes per document
