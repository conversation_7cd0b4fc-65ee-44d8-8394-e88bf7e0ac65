# Comprehensive System Test Guide

This guide explains how to run comprehensive system tests for document ingestion to RAG and Knowledge Graph in the LongevityCo platform.

## Overview

The system tests verify the complete document ingestion pipeline:

1. **Docker compose up with build** - Starts all services with fresh builds
2. **Database cleanup** - Removes all old data from PostgreSQL and Neo4j
3. **Automated document ingestion** - Uses API endpoints with batch processing
4. **RAG chunks verification** - Checks that RAG chunks are created
5. **KG chunks verification** - Checks that KG chunks are created  
6. **Entity extraction verification** - Verifies entities are extracted, normalized and stored in PostgreSQL
7. **Neo4j verification** - Checks that Neo4j contains entities, relationships, chunks, and documents

## Test Data

The tests ingest the following documents:

- **PubMed articles**: 2 articles with query "longevity diet"
- **sample_txt.txt**: Text file sample
- **sample_epub.epub**: EPUB file sample  
- **sample_pdf.pdf**: PDF file sample
- **sample_url.txt**: Contains URL to fetch and ingest

## Running the Tests

### Method 1: Shell Script (Recommended)

```bash
# From project root directory
./run_system_test.sh

# With verbose output
./run_system_test.sh --verbose

# With custom timeout (default: 1800 seconds = 30 minutes)
./run_system_test.sh --timeout=3600
```

### Method 2: Python Script

```bash
# From project root directory
python scripts/run_system_test.py

# With options
python scripts/run_system_test.py --verbose --timeout=3600
```

### Method 3: Simple Test Runner

```bash
# From project root directory
python test_system.py
```

### Method 4: Direct pytest

```bash
# From project root directory
export PYTHONPATH=$(pwd)
python -m pytest tests/system/test_comprehensive_system.py -v -s
```

## Prerequisites

1. **Docker and Docker Compose** installed and running
2. **Python environment** with required dependencies
3. **Test data files** in `tests/data/` directory
4. **Environment configuration** with proper `.env` file
5. **docker-compose.vm.yml** file in project root

## Expected Timeline

- **Setup**: 2-5 minutes (Docker build and startup)
- **Database cleanup**: 30 seconds
- **Document ingestion**: 2-5 minutes (API calls)
- **Processing**: 15-25 minutes (batch processing with Vertex AI)
- **Verification**: 1-2 minutes

**Total**: ~30 minutes (can be up to 45 minutes for large documents)

## What Gets Tested

### 1. API Health Check
- Verifies API is responding
- Checks database connections
- Confirms all services are healthy

### 2. PubMed Article Ingestion
- Searches for "longevity diet" articles
- Ingests 2 PubMed articles
- Uses automated batch processing

### 3. Sample File Ingestion
- Uploads text, EPUB, PDF files
- Ingests URL from sample_url.txt
- Triggers automated processing pipeline

### 4. Processing Monitoring
- Monitors Celery workers
- Waits for Vertex AI batch jobs
- Checks processing completion

### 5. RAG Chunks Verification
- Verifies chunks table has data
- Checks chunks per document
- Validates chunk content

### 6. KG Chunks Verification
- Verifies kg_chunks table has data
- Checks KG chunks per document
- Validates KG chunk structure

### 7. Entity Extraction Verification
- Checks entities table has data
- Verifies entity types extracted
- Confirms entity normalization

### 8. Neo4j Data Verification
- Checks Neo4j has entities
- Verifies entity-entity relationships
- Confirms entity-chunk relationships
- Validates document-chunk relationships

## Troubleshooting

### Common Issues

1. **Docker services not starting**
   ```bash
   docker-compose -f docker-compose.vm.yml logs
   ```

2. **Database connection errors**
   ```bash
   # Check PostgreSQL
   docker-compose -f docker-compose.vm.yml logs postgres
   
   # Check Neo4j
   docker-compose -f docker-compose.vm.yml logs neo4j
   ```

3. **Worker not processing**
   ```bash
   docker-compose -f docker-compose.vm.yml logs worker
   ```

4. **API not responding**
   ```bash
   docker-compose -f docker-compose.vm.yml logs api
   ```

### Manual Verification

You can manually check results:

1. **PostgreSQL**:
   ```sql
   SELECT COUNT(*) FROM documents;
   SELECT COUNT(*) FROM chunks;
   SELECT COUNT(*) FROM kg_chunks;
   SELECT COUNT(*) FROM entities;
   ```

2. **Neo4j** (http://localhost:7474):
   ```cypher
   MATCH (n) RETURN labels(n), count(n);
   MATCH ()-[r]->() RETURN type(r), count(r);
   ```

3. **API endpoints**:
   ```bash
   curl http://localhost:8000/health
   curl http://localhost:8000/api/documents/list
   ```

### Environment Variables

Ensure these are set in your `.env` file:

```bash
# Database
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=longevity
POSTGRES_USER=longevity
POSTGRES_PASSWORD=your_password

# Neo4j
NEO4J_HOST=localhost
NEO4J_PORT=7687
NEO4J_AUTH=neo4j/your_password

# Redis
REDIS_PASSWORD=your_redis_password

# API
API_PORT=8000
```

## Success Criteria

The tests pass when:

- ✅ All services are healthy
- ✅ Documents are ingested successfully
- ✅ RAG chunks are created for all documents
- ✅ KG chunks are created for all documents
- ✅ Entities are extracted and normalized
- ✅ Neo4j contains entities, relationships, and chunks
- ✅ Workers complete batch processing jobs

## Notes

- Tests are designed to be idempotent (can be run multiple times)
- Database cleanup happens automatically before each run
- Processing timeout is generous (30 minutes) to handle batch processing
- Tests will skip missing optional dependencies
- Partial results are verified even if processing times out

## Getting Help

If tests fail:

1. Check the logs for specific error messages
2. Verify all prerequisites are met
3. Ensure environment variables are correct
4. Try running individual test methods
5. Check Docker container health and logs
