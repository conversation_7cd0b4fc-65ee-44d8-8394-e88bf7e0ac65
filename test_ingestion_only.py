#!/usr/bin/env python3
"""
Simple ingestion test that assumes services are already running.
This skips the Docker setup and just tests the ingestion pipeline.
"""

import os
import sys
import time
import logging
import requests
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from common.config import settings
from common.database import get_db
from sqlalchemy import text

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Test configuration
PUBMED_QUERY = "longevity diet"
PUBMED_MAX_RESULTS = 2
API_BASE_URL = f"http://localhost:{settings.API_PORT}"
TEST_DATA_DIR = Path(__file__).parent / "tests" / "data"
SAMPLE_FILES = [
    "sample_txt.txt",
    "sample_epub.epub", 
    "sample_pdf.pdf",
    "sample_url.txt"
]


def test_api_health():
    """Test API health check."""
    logger.info("=== Testing API Health ===")
    
    response = requests.get(f"{API_BASE_URL}/health")
    assert response.status_code == 200
    
    health_data = response.json()
    logger.info(f"Health check response: {health_data}")
    status = health_data["status"]
    assert status in ["healthy", "degraded"], f"Expected healthy or degraded status, got: {status}"
    
    logger.info("✅ API health check passed")


def test_pubmed_ingestion():
    """Test PubMed article ingestion."""
    logger.info("=== Testing PubMed Ingestion ===")
    
    data = {
        'query': PUBMED_QUERY,
        'max_results': PUBMED_MAX_RESULTS
    }
    
    logger.info(f"Requesting PubMed articles with query: '{PUBMED_QUERY}', max_results: {PUBMED_MAX_RESULTS}")
    response = requests.post(
        f"{API_BASE_URL}/pubmed/insert_articles_with_query",
        json=data,
        timeout=120
    )
    
    assert response.status_code == 200
    result = response.json()
    
    logger.info(f"PubMed ingestion response: {result}")
    assert "task_ids" in result
    assert len(result["task_ids"]) > 0
    
    logger.info(f"✅ PubMed ingestion started with {len(result['task_ids'])} tasks")
    return result["task_ids"]


def test_file_ingestion():
    """Test file ingestion."""
    logger.info("=== Testing File Ingestion ===")
    
    task_ids = []
    success_count = 0
    
    for filename in SAMPLE_FILES:
        file_path = TEST_DATA_DIR / filename
        
        if not file_path.exists():
            logger.warning(f"Sample file not found: {file_path}")
            continue
        
        try:
            if filename == "sample_url.txt":
                # Handle URL file specially
                success, task_id = ingest_url_file(file_path)
            else:
                # Handle regular file upload
                success, task_id = ingest_file(file_path)
            
            if success:
                success_count += 1
                if task_id:
                    task_ids.append(task_id)
                    
        except Exception as e:
            logger.error(f"Error ingesting {filename}: {e}")
    
    assert success_count > 0, "No files were successfully ingested"
    logger.info(f"✅ Successfully started ingestion for {success_count}/{len(SAMPLE_FILES)} files")
    return task_ids


def ingest_file(file_path: Path):
    """Ingest a single file."""
    logger.info(f"Ingesting file: {file_path.name}")
    
    try:
        url = f"{API_BASE_URL}/api/documents/ingest"
        
        with open(file_path, 'rb') as f:
            files = {'file': f}
            response = requests.post(url, files=files, timeout=120)
            response.raise_for_status()
        
        result = response.json()
        task_id = result.get("task_id")
        
        if task_id:
            logger.info(f"File {file_path.name} ingestion started with task ID: {task_id}")
            return True, task_id
        else:
            logger.warning(f"No task ID returned for {file_path.name}")
            return False, None
            
    except requests.exceptions.RequestException as e:
        logger.error(f"Failed to ingest file {file_path.name}: {e}")
        return False, None


def ingest_url_file(file_path: Path):
    """Ingest URL from a text file."""
    logger.info(f"Ingesting URL from file: {file_path.name}")
    
    try:
        # Read URL from file
        with open(file_path, 'r') as f:
            url_to_ingest = f.read().strip()
        
        if not url_to_ingest:
            logger.error(f"No URL found in {file_path.name}")
            return False, None
        
        logger.info(f"Ingesting URL: {url_to_ingest}")
        
        # Send URL to ingest endpoint
        api_url = f"{API_BASE_URL}/api/documents/ingest"
        data = {'url': url_to_ingest}
        
        response = requests.post(api_url, data=data, timeout=120)
        response.raise_for_status()
        
        result = response.json()
        task_id = result.get("task_id")
        
        if task_id:
            logger.info(f"URL ingestion started with task ID: {task_id}")
            return True, task_id
        else:
            logger.warning(f"No task ID returned for URL ingestion")
            return False, None
            
    except Exception as e:
        logger.error(f"Failed to ingest URL from {file_path.name}: {e}")
        return False, None


def wait_and_verify_data(task_ids, timeout=600):
    """Wait for processing and verify data."""
    logger.info("=== Waiting for Processing and Verifying Data ===")
    
    start_time = time.time()
    
    # Wait a bit for processing to start
    logger.info("Waiting for processing to begin...")
    time.sleep(30)
    
    while time.time() - start_time < timeout:
        try:
            # Check if we have some data
            if check_postgres_data():
                logger.info("✅ Processing appears to be working - found data in PostgreSQL")
                break
        except Exception as e:
            logger.warning(f"Error checking data: {e}")
        
        logger.info("Still waiting for processing...")
        time.sleep(30)
    
    # Verify final results
    verify_rag_chunks()
    verify_kg_chunks()
    verify_entities()
    verify_neo4j_data()


def check_postgres_data():
    """Check if PostgreSQL has data."""
    db = next(get_db())
    try:
        # Check documents
        result = db.execute(text("SELECT COUNT(*) FROM documents"))
        doc_count = result.scalar()
        
        # Check chunks
        result = db.execute(text("SELECT COUNT(*) FROM chunks"))
        chunk_count = result.scalar()
        
        # Check KG chunks
        result = db.execute(text("SELECT COUNT(*) FROM kg_chunks"))
        kg_chunk_count = result.scalar()
        
        db.close()
        
        logger.info(f"PostgreSQL data: {doc_count} documents, {chunk_count} chunks, {kg_chunk_count} KG chunks")
        
        return doc_count > 0 and chunk_count > 0
        
    except Exception as e:
        logger.warning(f"Error checking PostgreSQL data: {e}")
        return False


def verify_rag_chunks():
    """Verify RAG chunks."""
    logger.info("=== Verifying RAG Chunks ===")
    
    db = next(get_db())
    try:
        result = db.execute(text("SELECT COUNT(*) FROM chunks"))
        total_chunks = result.scalar()
        
        logger.info(f"Total RAG chunks found: {total_chunks}")
        assert total_chunks > 0, "No RAG chunks found in database"
        
        logger.info("✅ RAG chunks verification passed")
        
    finally:
        db.close()


def verify_kg_chunks():
    """Verify KG chunks."""
    logger.info("=== Verifying KG Chunks ===")
    
    db = next(get_db())
    try:
        result = db.execute(text("SELECT COUNT(*) FROM kg_chunks"))
        total_kg_chunks = result.scalar()
        
        logger.info(f"Total KG chunks found: {total_kg_chunks}")
        assert total_kg_chunks > 0, "No KG chunks found in database"
        
        logger.info("✅ KG chunks verification passed")
        
    finally:
        db.close()


def verify_entities():
    """Verify entities."""
    logger.info("=== Verifying Entities ===")
    
    db = next(get_db())
    try:
        result = db.execute(text("SELECT COUNT(*) FROM entities"))
        total_entities = result.scalar()
        
        logger.info(f"Total entities found: {total_entities}")
        assert total_entities > 0, "No entities found in database"
        
        logger.info("✅ Entity verification passed")
        
    finally:
        db.close()


def verify_neo4j_data():
    """Verify Neo4j data."""
    logger.info("=== Verifying Neo4j Data ===")
    
    try:
        from neo4j import GraphDatabase
        
        driver = GraphDatabase.driver(
            settings.NEO4J_URI,
            auth=(settings.NEO4J_USER, settings.NEO4J_PASSWORD)
        )
        
        with driver.session() as session:
            # Check entities
            result = session.run("MATCH (e:Entity) RETURN count(e) as count")
            entity_count = result.single()["count"]
            logger.info(f"Neo4j entities: {entity_count}")
            
            # Check documents
            result = session.run("MATCH (d:Document) RETURN count(d) as count")
            document_count = result.single()["count"]
            logger.info(f"Neo4j documents: {document_count}")
            
            # Check chunks
            result = session.run("MATCH (c:Chunk) RETURN count(c) as count")
            chunk_count = result.single()["count"]
            logger.info(f"Neo4j chunks: {chunk_count}")
        
        driver.close()
        logger.info("✅ Neo4j verification completed")
        
    except ImportError:
        logger.warning("Neo4j driver not available - skipping Neo4j verification")
    except Exception as e:
        logger.warning(f"Neo4j verification failed: {e}")


def main():
    """Main function."""
    logger.info("🚀 Starting Simple Ingestion Test")
    logger.info("=" * 60)
    
    try:
        # Test API health
        test_api_health()
        
        # Test ingestion
        pubmed_tasks = test_pubmed_ingestion()
        file_tasks = test_file_ingestion()
        
        all_tasks = pubmed_tasks + file_tasks
        logger.info(f"Total tasks started: {len(all_tasks)}")
        
        # Wait and verify
        wait_and_verify_data(all_tasks)
        
        logger.info("🎉 All tests completed successfully!")
        return 0
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
