# Comprehensive System Tests

This directory contains comprehensive system tests for the LongevityCo document ingestion pipeline that tests the complete flow from API ingestion to RAG and Knowledge Graph storage.

## Test Overview

The comprehensive system test (`test_comprehensive_system.py`) follows the specified requirements:

1. **Docker compose up with build** - Starts all services with fresh builds
2. **Database cleanup** - Removes all old data from PostgreSQL and Neo4j
3. **Automated document ingestion** - Uses API endpoints with batch processing
4. **RAG chunks verification** - Checks that RAG chunks are created
5. **KG chunks verification** - Checks that KG chunks are created  
6. **Entity extraction verification** - Verifies entities are extracted, normalized and stored in PostgreSQL
7. **Neo4j verification** - Checks that Neo4j contains entities, relationships, chunks, and documents

## Test Files

The tests ingest the following sample documents:

- **PubMed articles** - 2 articles with query "longevity diet"
- **sample_txt.txt** - Text file sample
- **sample_epub.epub** - EPUB file sample
- **sample_pdf.pdf** - PDF file sample
- **sample_url.txt** - Contains URL to fetch and ingest

## Running the Tests

### Option 1: Using the Shell Script (Recommended)

```bash
# From project root directory
./run_system_test.sh

# With verbose output
./run_system_test.sh --verbose

# With custom timeout (default: 1800 seconds = 30 minutes)
./run_system_test.sh --timeout=3600
```

### Option 2: Using the Python Script

```bash
# From project root directory
python scripts/run_system_test.py

# With options
python scripts/run_system_test.py --verbose --timeout=3600
```

### Option 3: Direct pytest

```bash
# From project root directory
export PYTHONPATH=$(pwd)
python -m pytest tests/system/test_comprehensive_system.py -v -s
```

## Test Sequence

The tests run in the following order:

1. **test_01_api_health_check** - Verifies API is responding
2. **test_02_ingest_pubmed_articles** - Ingests PubMed articles
3. **test_03_ingest_sample_files** - Ingests sample files
4. **test_04_monitor_processing_completion** - Monitors until processing completes
5. **test_05_verify_rag_chunks** - Verifies RAG chunks are created
6. **test_06_verify_kg_chunks** - Verifies KG chunks are created
7. **test_07_verify_entities_extracted** - Verifies entity extraction and normalization
8. **test_08_verify_neo4j_data** - Verifies Neo4j contains expected data

## Configuration

Key configuration parameters:

- **PUBMED_QUERY**: "longevity diet"
- **PUBMED_MAX_RESULTS**: 2
- **PROCESSING_TIMEOUT**: 1800 seconds (30 minutes)
- **CHECK_INTERVAL**: 30 seconds

## Prerequisites

1. **Docker and Docker Compose** - For running services
2. **Python environment** - With required dependencies
3. **Test data files** - Located in `tests/data/`
4. **Environment configuration** - Proper `.env` file setup

## Expected Results

After successful completion, you should see:

- ✅ Documents ingested and stored in PostgreSQL
- ✅ RAG chunks created for each document
- ✅ KG chunks created for each document
- ✅ Entities extracted and normalized in PostgreSQL
- ✅ Neo4j populated with entities, relationships, chunks, and documents
- ✅ Workers processing batch jobs from Vertex AI

## Troubleshooting

### Common Issues

1. **Services not ready** - Increase timeout or check Docker logs
2. **Database connection errors** - Verify PostgreSQL and Neo4j are running
3. **Missing test files** - Check `tests/data/` directory
4. **Worker not processing** - Check Celery worker logs

### Checking Logs

```bash
# Check all service logs
docker-compose -f docker-compose.vm.yml logs

# Check specific service logs
docker-compose -f docker-compose.vm.yml logs api
docker-compose -f docker-compose.vm.yml logs worker
docker-compose -f docker-compose.vm.yml logs postgres
docker-compose -f docker-compose.vm.yml logs neo4j
```

### Manual Verification

You can manually verify the results by:

1. **PostgreSQL**: Connect and check tables `documents`, `chunks`, `kg_chunks`, `entities`
2. **Neo4j**: Open browser at http://localhost:7474 and run Cypher queries
3. **API**: Check document status via API endpoints

## Environment Variables

The tests use the following environment variables:

- `APP_ENV` - Application environment (should be set for proper database connections)
- `API_PORT` - API port (default: 8000)
- Database connection variables (PostgreSQL, Neo4j, Redis)

## Notes

- Tests are designed to be idempotent - they clean up before running
- Processing timeout is generous (30 minutes) to handle batch processing
- Tests will skip missing optional dependencies (e.g., Neo4j driver)
- Partial results are still verified even if processing times out
