#!/usr/bin/env python3
"""
Comprehensive system tests for document ingestion to RAG and Knowledge Graph.

This test suite follows the specified requirements:
1. <PERSON><PERSON> compose up with build
2. Remove all old data in databases
3. Start ingesting with full automation after calling ingest endpoint
4. Check RAG chunks and KG chunks are created
5. Verify entities extracted, normalized and inserted in PostgreSQL
6. Check Neo4j contains entities, relationships, chunks, and documents

Test files:
- PubMed articles with query "longevity diet" (2 articles)
- sample_txt.txt
- sample_epub.epub
- sample_pdf.pdf
- sample_url.txt (contains URL to fetch)
"""

import os
import sys
import pytest
import time
import requests
import json
import subprocess
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from common.config import settings
from common.database import get_db
from sqlalchemy import text

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Test configuration
PUBMED_QUERY = "longevity diet"
PUBMED_MAX_RESULTS = 2
PROCESSING_TIMEOUT = 1800  # 30 minutes
CHECK_INTERVAL = 30  # 30 seconds
SAMPLE_FILES = [
    "sample_txt.txt",
    "sample_epub.epub", 
    "sample_pdf.pdf",
    "sample_url.txt"
]


class TestComprehensiveSystem:
    """Comprehensive system tests for document ingestion pipeline."""
    
    @classmethod
    def setup_class(cls):
        """Set up test environment."""
        cls.api_base_url = f"http://localhost:{settings.API_PORT}"
        cls.test_data_dir = Path(__file__).parent.parent / "data"
        cls.ingestion_tasks = []
        cls.document_ids = []
        
        logger.info("=== STARTING COMPREHENSIVE SYSTEM TEST ===")
        
        # Step 1: Docker compose up with build
        cls._docker_compose_up_build()
        
        # Step 2: Wait for services and verify API
        cls._wait_for_services()
        
        # Step 3: Clean databases
        cls._cleanup_databases()
    
    @classmethod
    def _docker_compose_up_build(cls):
        """Step 1: Start Docker Compose services with build."""
        logger.info("=== STEP 1: Starting Docker Compose services with build ===")
        
        try:
            # Stop any existing containers
            logger.info("Stopping existing containers...")
            subprocess.run(
                ["docker-compose", "-f", "docker-compose.vm.yml", "down"], 
                check=False, 
                capture_output=True
            )
            
            # Start services with build
            logger.info("Starting services with build...")
            result = subprocess.run(
                ["docker-compose", "-f", "docker-compose.vm.yml", "up", "-d", "--build"],
                check=True,
                capture_output=True,
                text=True
            )
            logger.info("Docker Compose services started successfully with build")
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to start Docker Compose: {e}")
            logger.error(f"STDOUT: {e.stdout}")
            logger.error(f"STDERR: {e.stderr}")
            raise Exception("Failed to start Docker Compose services")
    
    @classmethod
    def _wait_for_services(cls, timeout: int = 300):
        """Wait for all services to be ready."""
        logger.info("=== Waiting for services to be ready ===")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                # Check API health
                response = requests.get(f"{cls.api_base_url}/health", timeout=10)
                if response.status_code == 200:
                    health_data = response.json()
                    logger.info(f"API health check: {health_data}")
                    
                    # Check if all services are healthy
                    if health_data.get("status") == "healthy":
                        logger.info("All services are ready")
                        return
                        
            except requests.exceptions.RequestException as e:
                logger.debug(f"API not ready yet: {e}")
            
            logger.info("Waiting for services...")
            time.sleep(10)
        
        raise Exception(f"Services did not become ready within {timeout} seconds")
    
    @classmethod
    def _cleanup_databases(cls):
        """Step 2: Clean all databases."""
        logger.info("=== STEP 2: Cleaning databases ===")
        
        try:
            # Run the purge script
            logger.info("Running database purge script...")
            result = subprocess.run(
                ["python", "purge_db_direct.py"],
                check=True,
                capture_output=True,
                text=True,
                cwd=Path(__file__).parent.parent.parent
            )
            logger.info("Databases cleaned successfully")
            logger.debug(f"Purge output: {result.stdout}")
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to clean databases: {e}")
            logger.error(f"STDOUT: {e.stdout}")
            logger.error(f"STDERR: {e.stderr}")
            raise Exception("Failed to clean databases")
    
    def test_01_api_health_check(self):
        """Test that API health check endpoint works."""
        logger.info("=== TEST: API Health Check ===")
        
        response = requests.get(f"{self.api_base_url}/health")
        assert response.status_code == 200
        
        health_data = response.json()
        logger.info(f"Health check response: {health_data}")
        assert health_data["status"] == "healthy"
        
        logger.info("✅ API health check passed")
    
    def test_02_ingest_pubmed_articles(self):
        """Test PubMed article ingestion with automated batch processing."""
        logger.info("=== TEST: PubMed Article Ingestion ===")
        
        # Ingest PubMed articles with "longevity diet" query
        data = {
            'query': PUBMED_QUERY,
            'max_results': PUBMED_MAX_RESULTS
        }
        
        logger.info(f"Requesting PubMed articles with query: '{PUBMED_QUERY}', max_results: {PUBMED_MAX_RESULTS}")
        response = requests.post(
            f"{self.api_base_url}/pubmed/insert_articles_with_query",
            json=data,
            timeout=120
        )
        
        assert response.status_code == 200
        result = response.json()
        
        logger.info(f"PubMed ingestion response: {result}")
        assert "task_ids" in result
        assert len(result["task_ids"]) > 0
        
        # Store task IDs for monitoring
        self.ingestion_tasks.extend(result["task_ids"])
        
        logger.info(f"✅ PubMed ingestion started with {len(result['task_ids'])} tasks")
    
    def test_03_ingest_sample_files(self):
        """Test sample file ingestion with automated batch processing."""
        logger.info("=== TEST: Sample File Ingestion ===")
        
        success_count = 0
        
        for filename in SAMPLE_FILES:
            file_path = self.test_data_dir / filename
            
            if not file_path.exists():
                logger.warning(f"Sample file not found: {file_path}")
                continue
            
            try:
                if filename == "sample_url.txt":
                    # Handle URL file specially
                    success = self._ingest_url_file(file_path)
                else:
                    # Handle regular file upload
                    success = self._ingest_file(file_path)
                
                if success:
                    success_count += 1
                    
            except Exception as e:
                logger.error(f"Error ingesting {filename}: {e}")
        
        assert success_count > 0, "No files were successfully ingested"
        logger.info(f"✅ Successfully started ingestion for {success_count}/{len(SAMPLE_FILES)} files")
    
    def _ingest_file(self, file_path: Path) -> bool:
        """Ingest a single file."""
        logger.info(f"Ingesting file: {file_path.name}")
        
        try:
            url = f"{self.api_base_url}/api/documents/ingest"
            
            with open(file_path, 'rb') as f:
                files = {'file': f}
                response = requests.post(url, files=files, timeout=120)
                response.raise_for_status()
            
            result = response.json()
            task_id = result.get("task_id")

            if task_id:
                logger.info(f"File {file_path.name} ingestion started with task ID: {task_id}")
                self.ingestion_tasks.append(task_id)
                return True
            else:
                logger.warning(f"No task ID returned for {file_path.name}")
                return False
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to ingest file {file_path.name}: {e}")
            return False
    
    def _ingest_url_file(self, file_path: Path) -> bool:
        """Ingest URL from a text file."""
        logger.info(f"Ingesting URL from file: {file_path.name}")
        
        try:
            # Read URL from file
            with open(file_path, 'r') as f:
                url_to_ingest = f.read().strip()
            
            if not url_to_ingest:
                logger.error(f"No URL found in {file_path.name}")
                return False
            
            logger.info(f"Ingesting URL: {url_to_ingest}")
            
            # Send URL to ingest endpoint
            api_url = f"{self.api_base_url}/api/documents/ingest"
            data = {'url': url_to_ingest}
            
            response = requests.post(api_url, data=data, timeout=120)
            response.raise_for_status()
            
            result = response.json()
            task_id = result.get("task_id")

            if task_id:
                logger.info(f"URL ingestion started with task ID: {task_id}")
                self.ingestion_tasks.append(task_id)
                return True
            else:
                logger.warning(f"No task ID returned for URL ingestion")
                return False
                
        except Exception as e:
            logger.error(f"Failed to ingest URL from {file_path.name}: {e}")
            return False


    def test_04_monitor_processing_completion(self):
        """Test monitoring of processing until completion."""
        logger.info("=== TEST: Monitor Processing Completion ===")

        if not self.ingestion_tasks:
            pytest.skip("No ingestion tasks to monitor")

        logger.info(f"Monitoring {len(self.ingestion_tasks)} ingestion tasks")

        start_time = time.time()

        while time.time() - start_time < PROCESSING_TIMEOUT:
            # Check if all processing is complete
            if self._check_processing_complete():
                logger.info("✅ All processing completed successfully!")
                return

            logger.info("Processing still in progress...")
            time.sleep(CHECK_INTERVAL)

        # If we reach here, processing timed out
        logger.error(f"Processing timeout after {PROCESSING_TIMEOUT} seconds")

        # Still run verification tests to see what was completed
        logger.info("Running verification tests on partial results...")

    def test_05_verify_rag_chunks(self):
        """Test that RAG chunks are created."""
        logger.info("=== TEST: Verify RAG Chunks ===")

        db = next(get_db())
        try:
            # Check total RAG chunks
            result = db.execute(text("SELECT COUNT(*) FROM chunks"))
            total_chunks = result.scalar()

            logger.info(f"Total RAG chunks found: {total_chunks}")
            assert total_chunks > 0, "No RAG chunks found in database"

            # Check chunks per document
            result = db.execute(text("""
                SELECT document_id, COUNT(*) as chunk_count
                FROM chunks
                GROUP BY document_id
            """))

            for row in result.fetchall():
                doc_id, chunk_count = row
                logger.info(f"Document {doc_id}: {chunk_count} RAG chunks")
                assert chunk_count > 0, f"No RAG chunks for document {doc_id}"

            logger.info("✅ RAG chunks verification passed")

        finally:
            db.close()

    def test_06_verify_kg_chunks(self):
        """Test that KG chunks are created."""
        logger.info("=== TEST: Verify KG Chunks ===")

        db = next(get_db())
        try:
            # Check total KG chunks
            result = db.execute(text("SELECT COUNT(*) FROM kg_chunks"))
            total_kg_chunks = result.scalar()

            logger.info(f"Total KG chunks found: {total_kg_chunks}")
            assert total_kg_chunks > 0, "No KG chunks found in database"

            # Check KG chunks per document
            result = db.execute(text("""
                SELECT document_id, COUNT(*) as kg_chunk_count
                FROM kg_chunks
                GROUP BY document_id
            """))

            for row in result.fetchall():
                doc_id, kg_chunk_count = row
                logger.info(f"Document {doc_id}: {kg_chunk_count} KG chunks")
                assert kg_chunk_count > 0, f"No KG chunks for document {doc_id}"

            logger.info("✅ KG chunks verification passed")

        finally:
            db.close()

    def test_07_verify_entities_extracted(self):
        """Test that entities are extracted, normalized and inserted in PostgreSQL."""
        logger.info("=== TEST: Verify Entities Extracted ===")

        db = next(get_db())
        try:
            # Check total entities
            result = db.execute(text("SELECT COUNT(*) FROM entities"))
            total_entities = result.scalar()

            logger.info(f"Total entities found: {total_entities}")
            assert total_entities > 0, "No entities found in database"

            # Check entity types
            result = db.execute(text("""
                SELECT entity_type, COUNT(*) as count
                FROM entities
                GROUP BY entity_type
                ORDER BY count DESC
            """))

            entity_types = {}
            for row in result.fetchall():
                entity_type, count = row
                entity_types[entity_type] = count
                logger.info(f"Entity type '{entity_type}': {count} entities")

            assert len(entity_types) > 0, "No entity types found"

            # Check normalized entities
            result = db.execute(text("""
                SELECT COUNT(*) FROM entities
                WHERE normalized_id IS NOT NULL
            """))
            normalized_count = result.scalar()

            logger.info(f"Normalized entities: {normalized_count}")
            # Note: Not all entities may be normalized, so we don't assert > 0

            logger.info("✅ Entity extraction verification passed")

        finally:
            db.close()

    def test_08_verify_neo4j_data(self):
        """Test that Neo4j contains entities, relationships, chunks, and documents."""
        logger.info("=== TEST: Verify Neo4j Data ===")

        try:
            # Import Neo4j driver
            from neo4j import GraphDatabase

            # Connect to Neo4j
            driver = GraphDatabase.driver(
                settings.NEO4J_URI,
                auth=(settings.NEO4J_USER, settings.NEO4J_PASSWORD)
            )

            with driver.session() as session:
                # Check entities
                result = session.run("MATCH (e:Entity) RETURN count(e) as count")
                entity_count = result.single()["count"]
                logger.info(f"Neo4j entities: {entity_count}")

                # Check documents
                result = session.run("MATCH (d:Document) RETURN count(d) as count")
                document_count = result.single()["count"]
                logger.info(f"Neo4j documents: {document_count}")

                # Check chunks
                result = session.run("MATCH (c:Chunk) RETURN count(c) as count")
                chunk_count = result.single()["count"]
                logger.info(f"Neo4j chunks: {chunk_count}")

                # Check entity-entity relationships
                result = session.run("MATCH (e1:Entity)-[r]-(e2:Entity) RETURN count(r) as count")
                entity_rel_count = result.single()["count"]
                logger.info(f"Neo4j entity-entity relationships: {entity_rel_count}")

                # Check entity-chunk relationships
                result = session.run("MATCH (e:Entity)-[r]-(c:Chunk) RETURN count(r) as count")
                entity_chunk_rel_count = result.single()["count"]
                logger.info(f"Neo4j entity-chunk relationships: {entity_chunk_rel_count}")

                # Check document-chunk relationships
                result = session.run("MATCH (d:Document)-[r]-(c:Chunk) RETURN count(r) as count")
                doc_chunk_rel_count = result.single()["count"]
                logger.info(f"Neo4j document-chunk relationships: {doc_chunk_rel_count}")

                # Verify we have some data
                assert entity_count > 0 or document_count > 0 or chunk_count > 0, \
                    "No data found in Neo4j"

                logger.info("✅ Neo4j data verification passed")

            driver.close()

        except ImportError:
            logger.warning("Neo4j driver not available - skipping Neo4j verification")
            pytest.skip("Neo4j driver not available")
        except Exception as e:
            logger.error(f"Neo4j verification failed: {e}")
            # Don't fail the test if Neo4j is not available
            pytest.skip(f"Neo4j verification failed: {e}")

    def _check_processing_complete(self) -> bool:
        """Check if all processing is complete by verifying data in databases."""
        try:
            # Check if we have documents, chunks, and entities
            postgres_ready = self._check_postgres_data()

            # For now, just check PostgreSQL data
            # Neo4j data might take longer to populate
            return postgres_ready

        except Exception as e:
            logger.warning(f"Error checking processing completion: {e}")
            return False

    def _check_postgres_data(self) -> bool:
        """Check if PostgreSQL has the expected data."""
        try:
            db = next(get_db())

            # Check documents
            result = db.execute(text("SELECT COUNT(*) FROM documents"))
            doc_count = result.scalar()

            # Check chunks
            result = db.execute(text("SELECT COUNT(*) FROM chunks"))
            chunk_count = result.scalar()

            # Check KG chunks
            result = db.execute(text("SELECT COUNT(*) FROM kg_chunks"))
            kg_chunk_count = result.scalar()

            # Check entities
            result = db.execute(text("SELECT COUNT(*) FROM entities"))
            entity_count = result.scalar()

            db.close()

            logger.debug(f"PostgreSQL data: {doc_count} documents, {chunk_count} chunks, {kg_chunk_count} KG chunks, {entity_count} entities")

            # We expect at least some data in each table
            return doc_count > 0 and chunk_count > 0 and kg_chunk_count > 0

        except Exception as e:
            logger.warning(f"Error checking PostgreSQL data: {e}")
            return False


if __name__ == "__main__":
    # Run tests directly
    pytest.main([__file__, "-v", "-s"])
