#!/usr/bin/env python3
"""
Test script to validate configuration consistency across different environments.
"""
import os
import sys
from pathlib import Path

# Add the project root to Python path
# Go up 3 levels: manual_tests -> system -> tests -> LongevityCo
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# Set PYTHONPATH environment variable as well
os.environ['PYTHONPATH'] = str(project_root)

def test_environment_config(app_env, env_file=None, expected_hosts=None):
    """Test configuration for a specific environment."""
    print(f"\n{'='*60}")
    print(f"Testing APP_ENV={app_env}")
    if env_file:
        print(f"Using env file: {env_file}")
    print(f"{'='*60}")
    
    # Clear any existing environment variables that might interfere
    env_vars_to_clear = [
        'POSTGRES_HOST', 'POSTGRES_PORT', 'REDIS_HOST', 'REDIS_PORT',
        'NEO4J_HOST', 'NEO4J_PORT', 'MINIO_HOST', 'MINIO_PORT'
    ]
    
    original_env = {}
    for var in env_vars_to_clear:
        original_env[var] = os.environ.pop(var, None)
    
    # Set APP_ENV
    os.environ['APP_ENV'] = app_env
    
    # Load environment file if specified
    if env_file and Path(env_file).exists():
        print(f"Loading environment from {env_file}...")
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    value = value.strip('"\'')
                    os.environ[key] = value
                    if key in env_vars_to_clear:
                        print(f"  {key}={value}")
    
    try:
        # Import settings after environment is set up
        from common.config import Settings
        settings = Settings()
        
        print(f"\nConfiguration Results:")
        print(f"  APP_ENV: {settings.APP_ENV}")
        print(f"  POSTGRES_HOST: {settings.POSTGRES_HOST}")
        print(f"  POSTGRES_PORT: {settings.POSTGRES_PORT}")
        print(f"  REDIS_HOST: {settings.REDIS_HOST}")
        print(f"  REDIS_PORT: {settings.REDIS_PORT}")
        print(f"  NEO4J_HOST: {settings.NEO4J_HOST}")
        print(f"  NEO4J_PORT: {settings.NEO4J_PORT}")
        print(f"  MINIO_HOST: {settings.MINIO_HOST}")
        print(f"  MINIO_PORT: {settings.MINIO_PORT}")
        print(f"  DATABASE_URL: {settings.DATABASE_URL}")
        print(f"  NEO4J_URI: {settings.NEO4J_URI}")
        
        # Validate against expected values if provided
        if expected_hosts:
            print(f"\nValidation:")
            for service, expected_host in expected_hosts.items():
                actual_host = getattr(settings, f"{service}_HOST")
                status = "✅" if actual_host == expected_host else "❌"
                print(f"  {service}_HOST: {actual_host} (expected: {expected_host}) {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        return False
    
    finally:
        # Restore original environment
        for var, value in original_env.items():
            if value is not None:
                os.environ[var] = value
            elif var in os.environ:
                del os.environ[var]

def main():
    """Run configuration tests for different environments."""
    print("Configuration Consistency Test")
    print("Testing different APP_ENV values and environment files...")
    
    test_cases = [
        {
            'app_env': 'docker',
            'env_file': '.env',
            'expected_hosts': {
                'POSTGRES': 'postgres',
                'REDIS': 'redis',
                'NEO4J': 'neo4j',
                'MINIO': 'minio'
            }
        },
        {
            'app_env': 'vm',
            'env_file': '.env.vm',
            'expected_hosts': {
                'POSTGRES': 'postgres',
                'REDIS': 'redis',
                'NEO4J': 'neo4j',
                'MINIO': 'minio'
            }
        },
        {
            'app_env': 'local',
            'env_file': None,
            'expected_hosts': {
                'POSTGRES': 'localhost',
                'REDIS': 'localhost',
                'NEO4J': 'localhost',
                'MINIO': 'localhost'
            }
        },
        {
            'app_env': 'local_vm',
            'env_file': None,
            'expected_hosts': {
                'POSTGRES': 'localhost',
                'REDIS': 'localhost',
                'NEO4J': 'localhost',
                'MINIO': 'localhost'
            }
        }
    ]
    
    results = []
    for test_case in test_cases:
        success = test_environment_config(**test_case)
        results.append((test_case['app_env'], success))
    
    print(f"\n{'='*60}")
    print("SUMMARY")
    print(f"{'='*60}")
    for app_env, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {app_env:15} {status}")
    
    all_passed = all(success for _, success in results)
    if all_passed:
        print(f"\n🎉 All configuration tests passed!")
        return 0
    else:
        print(f"\n💥 Some configuration tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
