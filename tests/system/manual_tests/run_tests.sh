#!/bin/bash

# Manual Testing Scripts Runner
# This script sets up the proper environment and runs the manual testing scripts

# Get the project root directory (4 levels up from this script)
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../../" && pwd)"

# Set PYTHONPATH to project root
export PYTHONPATH="$PROJECT_ROOT"

echo "🔧 Manual Testing Scripts Runner"
echo "================================"
echo "Project Root: $PROJECT_ROOT"
echo "PYTHONPATH: $PYTHONPATH"
echo ""

# Function to run a test script
run_test() {
    local script_name="$1"
    local description="$2"
    
    echo "🧪 Running: $description"
    echo "Script: $script_name"
    echo "----------------------------------------"
    
    cd "$PROJECT_ROOT"
    python "$SCRIPT_DIR/$script_name"
    local exit_code=$?
    
    echo ""
    if [ $exit_code -eq 0 ]; then
        echo "✅ $description: PASSED"
    else
        echo "❌ $description: FAILED (exit code: $exit_code)"
    fi
    echo ""
    
    return $exit_code
}

# Check if specific test was requested
if [ $# -eq 1 ]; then
    case "$1" in
        "config")
            run_test "test_config_consistency.py" "Configuration Consistency Test"
            ;;
        "validation")
            run_test "test_system_validation.py" "System Validation Test"
            ;;
        "fixes")
            run_test "validate_fixes.py" "Fixes Validation Test"
            ;;
        *)
            echo "❌ Unknown test: $1"
            echo "Available tests: config, validation, fixes"
            exit 1
            ;;
    esac
else
    # Run all tests
    echo "🚀 Running all manual tests..."
    echo ""
    
    total_tests=0
    passed_tests=0
    
    # Test 1: Fixes Validation
    run_test "validate_fixes.py" "Fixes Validation Test"
    total_tests=$((total_tests + 1))
    if [ $? -eq 0 ]; then
        passed_tests=$((passed_tests + 1))
    fi
    
    # Test 2: Configuration Consistency
    run_test "test_config_consistency.py" "Configuration Consistency Test"
    total_tests=$((total_tests + 1))
    if [ $? -eq 0 ]; then
        passed_tests=$((passed_tests + 1))
    fi
    
    # Test 3: System Validation
    run_test "test_system_validation.py" "System Validation Test"
    total_tests=$((total_tests + 1))
    if [ $? -eq 0 ]; then
        passed_tests=$((passed_tests + 1))
    fi
    
    # Summary
    echo "========================================"
    echo "📊 MANUAL TESTING SUMMARY"
    echo "========================================"
    echo "Total Tests: $total_tests"
    echo "Passed: $passed_tests"
    echo "Failed: $((total_tests - passed_tests))"
    echo ""
    
    if [ $passed_tests -eq $total_tests ]; then
        echo "🎉 All manual tests passed!"
        exit 0
    else
        echo "⚠️  Some manual tests failed."
        exit 1
    fi
fi
