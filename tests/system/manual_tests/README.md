# Manual Testing Scripts

This directory contains manual testing scripts for validating the LongevityCo system functionality and fixes.

## 📁 Available Scripts

### 1. `test_config_consistency.py`
**Purpose:** Validates configuration consistency across different environments.

**Usage:**
```bash
cd /home/<USER>/LongevityCo
python tests/system/manual_tests/test_config_consistency.py
```

**What it tests:**
- Configuration loading for `docker`, `vm`, `local`, and `local_vm` environments
- Environment file loading (`.env`, `.env.vm`)
- Host configuration validation
- Database URL generation

**Expected output:** All environment configurations should load correctly with appropriate host settings.

### 2. `test_system_validation.py`
**Purpose:** Quick system health check to verify core functionality.

**Usage:**
```bash
cd /home/<USER>/LongevityCo
python tests/system/manual_tests/test_system_validation.py
```

**What it tests:**
- API health endpoint (`/health`)
- API documentation endpoint (`/docs`)
- Worker container status
- Original error fixes verification
- File upload functionality

**Expected output:** All tests should pass, indicating the system is operational.

### 3. `validate_fixes.py`
**Purpose:** Validates that specific fixes implemented are working correctly.

**Usage:**
```bash
cd /home/<USER>/LongevityCo
python tests/system/manual_tests/validate_fixes.py
```

**What it tests:**
- TaskType enum availability (KNOWLEDGE_GRAPH_BUILDING)
- Configuration loading without errors
- DataTransport method existence
- API connectivity
- Worker container status

**Expected output:** All fixes should be validated as working correctly.

## 🚀 Quick Start

### Prerequisites
1. System should be running:
   ```bash
   cd /home/<USER>/LongevityCo
   docker-compose -f docker-compose.vm.yml up -d
   ```

2. Wait for services to be ready (30-60 seconds)

### Run All Tests
```bash
cd /home/<USER>/LongevityCo

# Test configuration consistency
python tests/system/manual_tests/test_config_consistency.py

# Validate system health
python tests/system/manual_tests/test_system_validation.py

# Validate specific fixes
python tests/system/manual_tests/validate_fixes.py
```

## 📊 Expected Results

### Configuration Consistency Test
```
Configuration Consistency Test
Testing different APP_ENV values and environment files...

============================================================
Testing APP_ENV=docker
Using env file: .env
============================================================
...
============================================================
SUMMARY
============================================================
  docker          ✅ PASS
  vm              ✅ PASS
  local           ✅ PASS
  local_vm        ✅ PASS

🎉 All configuration tests passed!
```

### System Validation Test
```
🔍 Running System Validation Tests
==================================================
🧪 Testing API health endpoint...
✅ API is responding (status: healthy)
...
==================================================
📊 VALIDATION SUMMARY
==================================================
API Health: ✅ PASSED
API Documentation: ✅ PASSED
Worker Status: ✅ PASSED
Original Errors Fixed: ✅ PASSED
File Upload: ✅ PASSED

Overall: 5/5 tests passed

🎉 System validation successful!
✅ The fixes are working and the system is operational
```

### Fixes Validation Test
```
🔍 Validating fixes for LongevityCo platform...
==================================================
🧪 Testing TaskType enum...
✅ KNOWLEDGE_GRAPH_BUILDING: knowledge_graph_building
...
==================================================
📊 VALIDATION SUMMARY
==================================================
TaskType Enum: ✅ PASSED
Configuration Loading: ✅ PASSED
DataTransport Methods: ✅ PASSED
API Connectivity: ✅ PASSED
Worker Status: ✅ PASSED

Overall: 5/5 tests passed

🎉 All fixes are working correctly!
✅ The system is ready for comprehensive testing
```

## 🐛 Troubleshooting

### Common Issues

**Issue: Import errors or module not found**
- Ensure you're running from the project root: `/home/<USER>/LongevityCo`
- Check that the system is running: `docker ps`

**Issue: API connectivity failures**
- Verify containers are running: `docker-compose -f docker-compose.vm.yml ps`
- Check API logs: `docker-compose -f docker-compose.vm.yml logs api`
- Wait longer for services to start up

**Issue: Worker status failures**
- Check worker container: `docker ps | grep worker`
- Check worker logs: `docker-compose -f docker-compose.vm.yml logs worker`
- Restart if needed: `docker-compose -f docker-compose.vm.yml restart worker`

**Issue: Configuration test failures**
- Verify environment files exist: `ls -la .env*`
- Check file permissions
- Ensure no conflicting environment variables are set

### Debug Commands

```bash
# Check all container status
docker-compose -f docker-compose.vm.yml ps

# Check specific service logs
docker-compose -f docker-compose.vm.yml logs api
docker-compose -f docker-compose.vm.yml logs worker

# Test API manually
curl http://localhost:8000/health

# Check worker container specifically
docker ps --filter name=longevityco-worker
```

## 📝 Notes

- These scripts are designed for manual testing and validation
- They complement the automated test suite in `tests/system/`
- Run these after making changes to verify system health
- Use these for quick smoke testing before comprehensive testing
- All scripts return appropriate exit codes (0 for success, 1 for failure)

## 🔗 Related Documentation

- Main testing guide: `TESTING_GUIDE.md`
- Implementation details: `VERTEX_AI_BATCH_MONITORING_IMPLEMENTATION.md`
- Comprehensive system tests: `tests/system/test_comprehensive_system.py`
