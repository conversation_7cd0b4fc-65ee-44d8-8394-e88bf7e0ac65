import logging
import time
import uuid
import re
import json
from typing import Dict, Any, Union, Optional, List
from google.cloud import aiplatform

from common.id_validator import validate_id_with_context, safe_uuid_conversion
from common.task_definitions import TaskStatus, TaskType
from sqlalchemy import or_
from sqlalchemy.dialects.postgresql import JSONB # For specific JSONB operations

from transport.data_transport import DataTransport
from transport.vertex_ai_batch_client import VertexAIBatchClient
from services.nlp_service import NLPService
from services.kg_entity_relationship_processor import EntityRelationshipProcessor
from common.database import ProcessingTask


# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class KnowledgeGraphService:
    """
    Service for building and maintaining knowledge graphs.
    Uses EntityRelationshipProcessor for handling entities and relationships.
    """

    def __init__(self, processor=None):
        """
        Initialize the knowledge graph service.

        Args:
            processor: Optional custom entity-relationship processor
        """
        self.processor = processor or EntityRelationshipProcessor()

    def _get_batch_job_state(self, batch_job_id: str) -> Optional[str]:
        """Gets the state of a Vertex AI batch prediction job."""
        try:
            vertex_client = VertexAIBatchClient()
            job_state = vertex_client.get_batch_job_state(batch_job_id)
            # Return the state name (e.g., 'JOB_STATE_SUCCEEDED') or None
            return job_state.name if job_state else None
        except Exception as e:
            logger.error(f"Error getting batch job state for {batch_job_id}: {e}")
            return None

    def build_knowledge_graph_from_document(self, document_id: Union[str, uuid.UUID],
                              task_id: Union[str, uuid.UUID],
                              async_mode: bool = True) -> Dict[str, Any]:
        """
        Build a knowledge graph from a document.

        Args:
            document_id: Document ID
            task_id: Processing task ID
            async_mode: Whether to process asynchronously

        Returns:
            Dict[str, Any]: Processing result
        """
        logger.info(f"Building knowledge graph for document: {document_id}, async_mode={async_mode}")

        # If async is requested, use the async method
        if async_mode:
            return self._build_async(document_id, task_id)

        # Otherwise use synchronous method
        try:
            with DataTransport() as transport:
                # Initialize Vertex AI client
                vertex_client = VertexAIBatchClient()
                # Use Neo4j client from DataTransport
                neo4j_client = transport.neo4j_client

                # 1. Get document and chunks
                transport.update_task_status(task_id=task_id, status=TaskStatus.PROCESSING.value)
                document = transport.db_client.get_document(document_id)
                if not document:
                    raise ValueError(f"Document not found: {document_id}")

                # Use KG chunks for knowledge graph building
                chunks = transport.get_document_chunks(document_id, chunk_type="kg")
                if not chunks:
                    logger.warning(f"No chunks found for document: {document_id}")
                    transport.update_task_status(task_id=task_id, status=TaskStatus.FAILED.value, result={"status": TaskStatus.FAILED.value, "message": "No document chunks found"})
                    return {"status": TaskStatus.FAILED.value, "message": "No document chunks found"}

                # 2. Store document in Neo4j
                neo4j_client.store_document(str(document.id), document.to_dict())

                # 3. Process KG chunks with Vertex AI
                logger.info(f"Processing {len(chunks)} KG chunks with Vertex AI")
                batch_job_info = vertex_client.batch_process_chunks(chunks, wait_for_results=True)
                chunk_results = vertex_client.get_batch_results(batch_job_info.get('job'))

                if not chunk_results:
                    logger.warning("No results returned from batch job")
                    transport.update_task_status(task_id=task_id, status=TaskStatus.COMPLETED.value, result={"status": TaskStatus.COMPLETED.value, "message": "No results from batch job", "entities_count": 0, "relationships_count": 0})
                    return {"status": TaskStatus.COMPLETED.value, "message": "No results from batch job"}

                # 4. Process chunk results with integrated processor
                entities, relationships = self.processor.process_batch_results(chunk_results, document_id)

                # 5. Deduplicate and normalize entities
                deduplicated_entities = NLPService.deduplicate_and_normalize_entities(
                    entities, str(document_id))

                # 6. Store entities in Neo4j
                # Group entities by chunk_id for batch processing
                entities_by_chunk = {}
                for entity in deduplicated_entities:
                    chunk_id = entity.get("chunk_id", "default_chunk")
                    if chunk_id not in entities_by_chunk:
                        entities_by_chunk[chunk_id] = []
                    entities_by_chunk[chunk_id].append(entity)

                # Store entities in Neo4j by chunk
                for chunk_id, chunk_entities in entities_by_chunk.items():
                    # Store entities for this chunk (convert chunk_id to string if needed)
                    neo4j_client.store_entities(str(chunk_id) if not isinstance(chunk_id, str) else chunk_id, chunk_entities)

                # Also store entities in PostgreSQL for searchability
                for entity in deduplicated_entities:
                    # Safely handle chunk_id conversion
                    chunk_id_value = None
                    if entity.get("chunk_id"):
                        try:
                            # Try to convert to UUID or use None
                            chunk_id_value = uuid.UUID(entity["chunk_id"])
                        except ValueError:
                            # Invalid UUID format, just use None
                            logger.warning(f"Invalid chunk_id format: {entity.get('chunk_id')}, using None instead")
                            chunk_id_value = None

                    # Check if entity already exists in PostgreSQL
                    entity_id = self._safe_uuid_conversion(entity["id"])
                    existing_entity = transport.db_client.get_entity(entity_id)

                    if not existing_entity:
                        # Store in PostgreSQL only if it doesn't already exist
                        try:
                            transport.db_client.create_entity(
                                id=entity_id,
                                document_id=self._safe_uuid_conversion(document_id),
                                kg_chunk_id=chunk_id_value,  # Use kg_chunk_id instead of chunk_id
                                text=entity["text"],
                                entity_type=entity["type"],
                                confidence=entity.get("score", 1.0)
                            )
                        except Exception as e:
                            logger.warning(f"Error creating entity {entity_id}: {e}")
                    else:
                        logger.info(f"Entity {entity_id} already exists, skipping creation")

                # 7. Store relationships in Neo4j (now have proper IDs)
                # Group relationships by chunk_id for batch processing
                relationships_by_chunk = {}
                for rel in relationships:
                    chunk_id = rel.get("chunk_id", "default_chunk")
                    if chunk_id not in relationships_by_chunk:
                        relationships_by_chunk[chunk_id] = []
                    relationships_by_chunk[chunk_id].append(rel)

                # Store relationships in Neo4j by chunk
                for chunk_id, chunk_relationships in relationships_by_chunk.items():
                    # Store relationships for this chunk (convert chunk_id to string if needed)
                    neo4j_client.store_relationships(str(chunk_id) if not isinstance(chunk_id, str) else chunk_id, chunk_relationships)

                # 8. Create relationships between sequential chunks
                self._create_chunk_relationships(chunks, document_id, neo4j_client)

                # 9. Complete processing
                result = {
                    "status": TaskStatus.COMPLETED.value,
                    "document_id": str(document_id),
                    "entities_count": len(deduplicated_entities),
                    "relationships_count": len(relationships),
                    "batch_job_id": batch_job_info.get('batch_job_id')
                }

                transport.update_task_status(
                    task_id=task_id,
                    status=TaskStatus.COMPLETED.value,
                    result=result
                )


                return result

        except Exception as e:
            logger.error(f"Error building knowledge graph: {e}", exc_info=True)

            try:
                with DataTransport() as transport:
                    transport.update_task_status(
                        task_id=task_id,
                        status=TaskStatus.FAILED.value,
                        result={"status": TaskStatus.FAILED.value, "error_message": str(e)},
                        error=str(e)
                    )
            except Exception:
                pass

            raise e

    def _build_async(self, document_id: Union[str, uuid.UUID],
                    task_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
        """
        Start asynchronous knowledge graph building process.

        Args:
            document_id: Document ID
            task_id: Processing task ID

        Returns:
            Dict[str, Any]: Job status information
        """
        try:
            with DataTransport() as transport:
                # Initialize Vertex AI client
                vertex_client = VertexAIBatchClient()
                # Use Neo4j client from DataTransport
                neo4j_client = transport.neo4j_client

                # 1. Get document and chunks
                # Initial status for async is also processing, as it sets up the batch job
                transport.update_task_status(task_id=task_id, status=TaskStatus.PROCESSING.value)
                document = transport.db_client.get_document(document_id)
                if not document:
                    raise ValueError(f"Document not found: {document_id}")

                # Use KG chunks for knowledge graph building
                chunks = transport.get_document_chunks(document_id, chunk_type="kg")
                if not chunks:
                    logger.warning(f"No chunks found for document: {document_id}")
                    transport.update_task_status(task_id=task_id, status=TaskStatus.FAILED.value, result={"status": TaskStatus.FAILED.value, "message": "No document chunks found"})
                    return {"status": TaskStatus.FAILED.value, "message": "No document chunks found"}

                # 2. Store document in Neo4j
                neo4j_client.store_document(str(document.id), document.to_dict())

                # 3. Submit batch job without waiting for results
                logger.info(f"Submitting async batch job for {len(chunks)} chunks")
                batch_job_info = vertex_client.batch_process_chunks(chunks=chunks, wait_for_results=False)

                # 4. Update task status with batch job info
                if batch_job_info.get('status') == 'submitted' and batch_job_info.get('batch_job_id'):
                    # If the batch job was created successfully, set the task status to batch_job_submitted
                    current_status = TaskStatus.BATCH_JOB_SUBMITTED.value
                    transport.update_task_status(
                        task_id=task_id,
                        status=current_status,
                        result={
                            "batch_job_id": batch_job_info.get('batch_job_id'),
                            "status": current_status, # Mirror main status in result
                            "created_at": time.time(),
                            "async_processing": True
                        }
                    )
                    return { # Return info about the submitted job
                        "status": current_status,
                        "document_id": str(document_id),
                        "task_id": str(task_id),
                        "batch_job_id": batch_job_info.get('batch_job_id'),
                        "message": "Knowledge graph building submitted asynchronously"
                    }
                else:
                    # If the status is neither error nor submitted, or there's no batch_job_id, set the task status to failed
                    current_status = TaskStatus.FAILED.value
                    transport.update_task_status(
                        task_id=task_id,
                        status=current_status,
                        result={
                            "status": current_status, # Mirror main status in result
                            "error_message": f"Unexpected batch job status: {batch_job_info.get('status')}",
                            "created_at": time.time(),
                            "async_processing": True,
                            "batch_job_info": batch_job_info
                        }
                    )
                    return {
                        "status": "error",
                        "message": f"Unexpected batch job status: {batch_job_info.get('status')}"
                    }

                return {
                    "status": "processing",
                    "document_id": str(document_id),
                    "task_id": str(task_id),
                    "batch_job_id": batch_job_info.get('batch_job_id'),
                    "message": "Knowledge graph building started asynchronously"
                }

        except Exception as e:
            logger.error(f"Error starting async knowledge graph build: {e}", exc_info=True)

            try:
                with DataTransport() as transport:
                    transport.update_task_status(
                        task_id=task_id,
                        status="failed",
                        error=str(e)
                    )
            except Exception:
                pass

            raise e

    def _get_task_and_document_data(self, transport: DataTransport, task_id: uuid.UUID) -> tuple:
        """Fetches task, document, and chunks, validating along the way."""
        task = transport.db_client.get_processing_task(task_id)
        if not task:
            raise ValueError(f"Task {task_id} not found")

        batch_job_id = task.result.get("batch_job_id") if task.result else None
        if not batch_job_id:
            raise ValueError("No batch job ID found in task")

        document_id = task.document_id
        if not transport.db_client.get_document(document_id):
            raise ValueError(f"Document {document_id} not found")

        chunks = transport.get_document_chunks(document_id, chunk_type="kg")
        logger.info(f"Retrieved {len(chunks)} KG chunks for document {document_id}")
        # Log the chunk IDs from the database
        logger.info(f"Chunks from database for document {document_id}:")
        for i, chunk in enumerate(chunks[:5]):  # Log first 5 chunks
            logger.info(f"  DB Chunk {i+1}: ID={chunk.get('id')}, Text={chunk.get('text')[:50]}...")
        if len(chunks) > 5:
            logger.info(f"  ... and {len(chunks) - 5} more chunks")

        return task, document_id, chunks, batch_job_id

    def _fetch_and_process_batch_results(self, vertex_client: VertexAIBatchClient, task: ProcessingTask, chunks: list, document_id: uuid.UUID) -> tuple:
        """Fetches batch results, processes them, and deduplicates entities."""
        batch_job_id = task.result.get("batch_job_id")

        logger.info(f"Getting results for batch job: {batch_job_id}")
        if "batch_results" in task.result:
            logger.info(f"Using batch results from task result")
            batch_results_str = task.result.get("batch_results")
            chunk_results = self._process_batch_results(batch_results_str, chunks)
        else:
            logger.info(f"Getting batch results from Vertex AI")
            chunk_results = vertex_client.get_batch_results(batch_job_id, chunks)

        logger.info(f"Retrieved {len(chunk_results)} chunk results")
        # Log the chunk IDs from the batch results
        logger.info(f"Chunk IDs from batch results:")
        for i, result in enumerate(chunk_results[:5]):  # Log first 5 results
            logger.info(f"  Result {i+1}: chunk_id={result.get('chunk_id')}")
        if len(chunk_results) > 5:
            logger.info(f"  ... and {len(chunk_results) - 5} more results")
        logger.info(f"First chunk result: {chunk_results[0] if chunk_results else None}")

        if not chunk_results:
            logger.warning("No results returned from batch job")
            return [], [] # Return empty lists if no results

        logger.info("Processing entities and relationships")
        entities, relationships = self.processor.process_batch_results(chunk_results, document_id)
        logger.info(f"Extracted {len(entities)} entities and {len(relationships)} relationships")
        logger.info(f"First entity: {entities[0] if entities else None}")
        logger.info(f"First relationship: {relationships[0] if relationships else None}")

        deduplicated_entities = NLPService.deduplicate_and_normalize_entities(
            entities, str(document_id))

        return deduplicated_entities, relationships

    def _store_graph_data(self, transport: DataTransport, neo4j_client, document_id: uuid.UUID, chunks: list, entities: list, relationships: list):
        """Stores entities (Neo4j & PG), relationships (Neo4j), and creates chunk relationships."""
        # Store entities in Neo4j
        logger.info(f"Storing {len(entities)} entities in Neo4j")
        entities_by_chunk = {}
        for entity in entities:
            chunk_id = entity.get("chunk_id", "default_chunk")
            if chunk_id not in entities_by_chunk:
                entities_by_chunk[chunk_id] = []
            entities_by_chunk[chunk_id].append(entity)

        for chunk_id, chunk_entities in entities_by_chunk.items():
            neo4j_client.store_entities(str(chunk_id) if not isinstance(chunk_id, str) else chunk_id, chunk_entities)

        # Store entities in PostgreSQL
        logger.info(f"Storing {len(entities)} entities in PostgreSQL")
        for entity in entities:
            chunk_id_value = None
            if entity.get("chunk_id"):
                try:
                    chunk_id_value = uuid.UUID(entity["chunk_id"])
                except ValueError:
                    logger.warning(f"Invalid chunk_id format: {entity.get('chunk_id')}, using None instead")
                    chunk_id_value = None

            entity_id = self._safe_uuid_conversion(entity["id"])
            existing_entity = transport.db_client.get_entity(entity_id)

            if not existing_entity:
                try:
                    transport.db_client.create_entity(
                        id=entity_id,
                        document_id=self._safe_uuid_conversion(document_id),
                        kg_chunk_id=chunk_id_value,
                        text=entity["text"],
                        entity_type=entity["type"],
                        confidence=entity.get("score", 1.0)
                    )
                except Exception as e:
                    logger.warning(f"Error creating entity {entity_id}: {e}")
            else:
                logger.info(f"Entity {entity_id} already exists, skipping creation")

        # Store relationships in Neo4j
        logger.info(f"Storing {len(relationships)} relationships in Neo4j")
        relationships_by_chunk = {}
        for rel in relationships:
            chunk_id = rel.get("chunk_id", "default_chunk")
            if chunk_id not in relationships_by_chunk:
                relationships_by_chunk[chunk_id] = []
            relationships_by_chunk[chunk_id].append(rel)

        for chunk_id, chunk_relationships in relationships_by_chunk.items():
            neo4j_client.store_relationships(str(chunk_id) if not isinstance(chunk_id, str) else chunk_id, chunk_relationships)

        # Create relationships between sequential chunks
        self._create_chunk_relationships(chunks, document_id, neo4j_client)

    def _finalize_task(self, transport: DataTransport, task_id: uuid.UUID, task_result: dict, entities_count: int, relationships_count: int, document_id: uuid.UUID) -> dict:
        """Updates the task status to completed with the final results."""
        final_status = TaskStatus.COMPLETED.value # Use Enum
        result = {
            **(task_result or {}),  # Preserve existing result data
            "status": final_status,  # Use Enum & Match the task status
            "document_id": str(document_id),
            "entities_count": entities_count,
            "relationships_count": relationships_count,
            "completed_at": time.time()
        }

        transport.update_task_status(
            task_id=task_id,
            status=final_status, # Use Enum
            result=result
        )
        logger.info(f"Task {task_id} {final_status} successfully.") # Use Enum in log
        return result

    def _handle_continuation_error(self, transport: DataTransport, task_id: uuid.UUID, error: Exception):
        """Handles errors during the continuation process."""
        logger.error(f"Error continuing async job for task {task_id}: {error}", exc_info=True)
        current_status = TaskStatus.FAILED.value # Use Enum
        try:
            task = transport.db_client.get_processing_task(task_id)
            if task:
                transport.update_task_status(
                    task_id=task_id,
                    status=current_status,
                    result={
                        **(task.result or {}),
                        "status": current_status, # Use Enum in result
                        "error_message": str(error),
                        "failed_at": time.time()
                    },
                    error=str(error)
                )
            else:
                transport.update_task_status(
                    task_id=task_id,
                    status=current_status,
                    result={"status": current_status, "error_message": str(error)}, # Use Enum in result
                    error=str(error)
                )
        except Exception as inner_e:
            logger.error(f"Error updating task status after failure: {inner_e}")

    def continue_knowledge_graph_building(self, task_id: Union[str, uuid.UUID], processed_entity_results: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        Continue knowledge graph building after batch job completes. Refactored for clarity.

        Args:
            task_id: Processing task ID

        Returns:
            Dict[str, Any]: Processing result
        """
        if isinstance(task_id, str):
            try:
                task_id = uuid.UUID(task_id)
            except ValueError:
                 return {"status": "error", "message": f"Invalid task ID format: {task_id}"}

        transport = None # Initialize transport to None
        try:
            with DataTransport() as transport:
                # 1. Get Task, Document, Chunks, and Batch Job ID
                task, document_id, chunks, batch_job_id = self._get_task_and_document_data(transport, task_id)

                # Set status to KG_PROCESSING_STARTED
                kg_processing_status = TaskStatus.KG_PROCESSING_STARTED.value # Use Enum
                current_task_result = task.result if task.result else {}
                transport.update_task_status(task_id=task_id, status=kg_processing_status, result={**current_task_result, "status": kg_processing_status, "kg_processing_started_at": time.time()})

                # Initialize clients
                vertex_client = VertexAIBatchClient()
                neo4j_client = transport.neo4j_client

                chunk_results = None
                if processed_entity_results:
                    logger.info(f"Using pre-fetched processed_entity_results for task {task_id}")
                    chunk_results = processed_entity_results
                else:
                    logger.info(f"Getting results for batch job: {batch_job_id} for task {task_id}")
                    if "batch_results" in task.result and task.result.get("batch_results"):
                        logger.info(f"Using batch results from task result for task {task_id}")
                        batch_results_str = task.result.get("batch_results")
                        chunk_results = self._process_batch_results(batch_results_str, chunks)
                    else:
                        logger.info(f"Getting batch results from Vertex AI for task {task_id}")
                        chunk_results = vertex_client.get_batch_results(batch_job_id, chunks)

                if not chunk_results:
                    logger.warning(f"No results (either pre-fetched or newly fetched) found for task {task_id}. Completing task.")
                    return self._finalize_task(transport, task_id, task.result, 0, 0, document_id)

                # 2. Process Batch Results (if not already processed)
                # If chunk_results came from processed_entity_results, they are already in the correct format.
                # If they came from other sources, they might need processing by EntityRelationshipProcessor.
                # The self.processor.process_batch_results should be robust enough to handle either.
                logger.info("Processing entities and relationships")
                entities, relationships = self.processor.process_batch_results(chunk_results, document_id)
                logger.info(f"Extracted {len(entities)} entities and {len(relationships)} relationships for task {task_id}")

                # If no entities/relationships were found (e.g., empty batch results),
                # we can consider the task successful but with no data.
                if not entities and not relationships:
                     logger.warning(f"No entities or relationships extracted for task {task_id}. Completing task.")
                     # Finalize with counts 0
                     return self._finalize_task(transport, task_id, task.result, 0, 0, document_id)


                # 3. Store Graph Data (Entities, Relationships, Chunk Links)
                self._store_graph_data(
                    transport, neo4j_client, document_id, chunks, entities, relationships
                )

                # 4. Finalize Task
                return self._finalize_task(
                    transport, task_id, task.result, len(entities), len(relationships), document_id
                )

        except ValueError as ve: # Catch specific validation errors
             logger.error(f"Validation error during continuation for task {task_id}: {ve}")
             # Attempt to update task status if transport was initialized
             if transport:
                 self._handle_continuation_error(transport, task_id, ve)
             return {"status": "error", "message": str(ve)}
        except Exception as e:
            # Use the dedicated error handler
            if transport: # Ensure transport is available for error handling
                 self._handle_continuation_error(transport, task_id, e)
            else: # Log if transport couldn't be initialized
                 logger.error(f"Critical error before transport initialization for task {task_id}: {e}", exc_info=True)
            # Re-raise the exception after attempting to handle it
            raise e

    def check_batch_job_status(self, task_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
        """
        Check the status of a batch job and update the database.
        This method is primarily a utility for direct status checks.
        The main KG task lifecycle is managed by check_and_continue_batch_jobs.
        """
        if isinstance(task_id, str):
            task_id = uuid.UUID(task_id)

        with DataTransport() as transport:
            task = transport.db_client.get_processing_task(task_id)
            if not task:
                return {"status": TaskStatus.FAILED.value, "message": f"Task {task_id} not found"}

            batch_job_id = task.result.get("batch_job_id") if task.result else None
            if not batch_job_id:
                return {"status": TaskStatus.FAILED.value, "message": "No batch job ID found in task"}

            try:
                job = aiplatform.BatchPredictionJob.get(batch_job_id)
                job_state_name = job.state.name if job.state else "UNKNOWN"

                if job.state == aiplatform.JobState.JOB_STATE_SUCCEEDED:
                    # Reflects that the batch job part is done.
                    current_status_val = TaskStatus.BATCH_JOB_COMPLETED.value
                    transport.update_task_status(
                        task_id=task_id,
                        status=current_status_val,
                        result={**task.result, "status": current_status_val, "job_check_status": "succeeded", "job_state": job_state_name, "completed_at": time.time()}
                    )
                    return {"status": current_status_val, "message": "Batch job completed successfully."}
                elif job.state in [aiplatform.JobState.JOB_STATE_FAILED, aiplatform.JobState.JOB_STATE_CANCELLED]:
                    current_status_val = TaskStatus.BATCH_JOB_FAILED.value
                    transport.update_task_status(
                        task_id=task_id,
                        status=current_status_val,
                        result={**task.result, "status": current_status_val, "job_check_status": "failed", "job_state": job_state_name, "error": f"Batch job failed/cancelled: {job_state_name}"}
                    )
                    return {"status": current_status_val, "message": f"Batch job failed or cancelled: {job_state_name}"}
                else: # Running, Pending, Queued etc.
                    current_status_val = TaskStatus.BATCH_JOB_PROCESSING.value
                    transport.update_task_status(
                        task_id=task_id,
                        status=current_status_val, # Update main task status if it's a KG task, otherwise this might just be an info update
                        result={**task.result, "status": current_status_val, "job_check_status": "processing", "job_state": job_state_name, "last_checked_at": time.time()}
                    )
                    return {"status": current_status_val, "message": f"Batch job is processing: {job_state_name}"}
            except Exception as e:
                logger.error(f"Error checking batch job status for task {task_id}: {e}")
                transport.update_task_status(
                    task_id=task_id,
                    status=TaskStatus.FAILED.value,
                    result={**(task.result or {}), "status": TaskStatus.FAILED.value, "job_check_status": "error_checking_status", "error_message": str(e)}
                )
                return {"status": TaskStatus.FAILED.value, "message": str(e)}

    def _create_chunk_relationships(self, chunks, document_id, neo4j_client):
        """Create relationships between chunks based on index order."""
        chunk_data = []

        for chunk in chunks:
            chunk_index = chunk.get("chunk_index", chunk.get("metadata", {}).get("element_id", 0))
            chunk_data.append({
                "id": str(chunk["id"]),
                "chunk_index": chunk_index
            })

        neo4j_client.create_chunk_relationships(str(document_id), chunk_data)

    def _safe_uuid_conversion(self, id_value):
        """Safely convert a string to UUID or return the original value."""
        # Use the imported safe_uuid_conversion function
        return safe_uuid_conversion(id_value)

    def _process_batch_results(self, batch_results_str: str, chunks: list = None) -> list:
        """
        Process batch results from a string representation.

        Args:
            batch_results_str: String representation of batch results
            chunks: Optional list of chunks for reference (not used in current implementation)

        Returns:
            list: Processed chunk results
        """
        import json
        import re

        logger.info(f"Processing batch results string of length {len(batch_results_str)}")

        # Process the batch results
        entity_results = {}

        try:
            # Split the batch results string into lines
            lines = batch_results_str.strip().split('\n')
            logger.info(f"Batch results contains {len(lines)} lines")

            for line_idx, line in enumerate(lines):
                if not line:
                    continue

                try:
                    # Parse the line as JSON
                    prediction = json.loads(line)
                    logger.info(f"Processing prediction line {line_idx+1}/{len(lines)}, keys: {list(prediction.keys())}")

                    # Extract response text from the API response format
                    response = prediction.get('response', {})
                    candidates = response.get('candidates', [])

                    if not candidates:
                        logger.warning("No candidates found in prediction response")
                        continue

                    content = candidates[0].get('content', {})
                    parts = content.get('parts', [])

                    if not parts:
                        logger.warning("No parts found in prediction response content")
                        continue

                    prediction_text = parts[0].get('text', '')

                    # The prediction text format is typically a markdown code block with JSON
                    # Extract the JSON content from the markdown
                    if prediction_text.startswith('```json') and prediction_text.endswith('```'):
                        json_text = prediction_text[7:-3].strip()
                    else:
                        json_text = prediction_text

                    try:
                        # Parse the extracted JSON
                        extracted_data = json.loads(json_text)

                        # Try to find the chunk ID in the extracted data
                        chunk_id = extracted_data.get('chunk_id', '')

                        # Log chunk ID extraction attempt
                        if chunk_id:
                            logger.info(f"Found chunk_id directly in extracted data: {chunk_id}")
                        else:
                            logger.warning(f"No chunk_id found in extracted data, attempting to extract from request")

                        # If chunk ID is not in the extracted data, try to find it in the request
                        if not chunk_id and 'request' in prediction:
                            request = prediction.get('request', {})
                            contents = request.get('contents', [])
                            if contents and 'parts' in contents[0]:
                                request_parts = contents[0]['parts']
                                if request_parts:
                                    request_text = request_parts[0].get('text', '')
                                    # Extract chunk_id from the request text using multiple patterns
                                    chunk_id_patterns = [
                                        r'"chunk_id":\s*"([^"]+)"',  # JSON format
                                        r'chunk_id:\s*"([^"]+)"',      # YAML-like format
                                        r'Chunk ID:\s*([\w-]+)',         # Plain text format
                                        r'chunk ID:\s*([\w-]+)'          # Case insensitive
                                    ]

                                    for pattern in chunk_id_patterns:
                                        chunk_id_match = re.search(pattern, request_text, re.IGNORECASE)
                                        if chunk_id_match:
                                            chunk_id = chunk_id_match.group(1)
                                            logger.info(f"Extracted chunk_id from request text using pattern '{pattern}': {chunk_id}")
                                            break

                                    if not chunk_id and request_text:
                                        logger.error(f"Failed to extract chunk_id from request text using all patterns")
                                        # Log a portion of the request text for debugging
                                        logger.debug(f"Request text excerpt: {request_text[:200]}...")

                        # Validate and clean the chunk ID if found
                        if chunk_id:
                            # Use the ID validator with context and error on invalid
                            chunk_id = validate_id_with_context(chunk_id, "Batch results processing", logger, error_on_invalid=True)

                            # Add the result to entity_results
                            entity_results[chunk_id] = {
                                'chunk_id': chunk_id,
                                'entities': extracted_data.get('entities', []),
                                'relationships': extracted_data.get('relationships', []),
                                'error': extracted_data.get('error', '')
                            }
                            logger.info(f"Processed prediction for chunk {chunk_id} with {len(extracted_data.get('entities', []))} entities and {len(extracted_data.get('relationships', []))} relationships")
                        else:
                            # This is a critical error - we should not proceed without a chunk ID
                            logger.error("Could not find or extract a valid chunk ID from the prediction")
                            logger.error(f"Prediction keys: {list(prediction.keys())}")
                            logger.error(f"Extracted data keys: {list(extracted_data.keys())}")
                            # We'll raise an exception here to stop processing and alert the system
                            raise ValueError("Missing chunk ID in prediction result - cannot continue processing")
                    except json.JSONDecodeError as e:
                        logger.error(f"Error parsing JSON from prediction text: {e}\nText: {prediction_text[:100]}...")
                        continue

                except json.JSONDecodeError as e:
                    logger.error(f"Error parsing prediction line as JSON: {e}")
                    continue

        except Exception as e:
            logger.error(f"Error processing batch results: {e}")

        # Convert entity_results to a list
        results = list(entity_results.values())

        return results

    def query_knowledge_graph(self, query_text: str, limit: int = 10) -> Dict[str, Any]:
        """Query the knowledge graph using natural language."""
        try:
            with DataTransport() as transport:
                # Use Neo4j client from DataTransport
                results = transport.neo4j_client.query_knowledge_graph(query_text, limit)
                return {
                    "query": query_text,
                    "results": results,
                    "count": len(results)
                }
        except Exception as e:
            logger.error(f"Error querying knowledge graph: {e}")
            raise

    # --- Start of Helper Methods ---

    def _get_actionable_kg_tasks(self, transport: DataTransport, limit: int) -> List[ProcessingTask]:
        """Queries for knowledge_graph_building tasks that are ready for status check."""
        logger.debug(f"Querying for actionable KG tasks (limit: {limit})")
        # Define target statuses including legacy ones for a transition period
        target_statuses = [
            TaskStatus.BATCH_JOB_SUBMITTED.value,
            TaskStatus.BATCH_JOB_PROCESSING.value,
            "processing",  # Legacy status
            "batch_job_created"  # Legacy status
        ]

        tasks = transport.db_client.db.query(ProcessingTask).filter(
            ProcessingTask.task_type == TaskType.KNOWLEDGE_GRAPH_BUILDING.value,
            ProcessingTask.status.in_(target_statuses),
            ProcessingTask.result.contains({"batch_job_id": None}).is_(False),  # Must have a batch_job_id
            ProcessingTask.result.contains({"async_processing": True})  # Must be async
        ).order_by(ProcessingTask.updated_at.asc()).limit(limit).all() # Order by updated_at to process older tasks first

        logger.debug(f"Found {len(tasks)} actionable KG tasks.")
        return tasks

    def _fetch_vertex_task(self, batch_job_id_from_kg_task: str, transport: DataTransport) -> Optional[ProcessingTask]:
        """Fetches the corresponding vertex_ai_batch_job task."""
        logger.debug(f"Fetching Vertex AI task for batch_job_id: {batch_job_id_from_kg_task}")
        # The batch_job_id in kg_task.result is the Vertex AI resource name.
        # In vertex_ai_batch_job tasks, this is stored in metadata.batch_job_id upon creation.
        # It's also mirrored to result.batch_job_id by the VertexAIBatchClient.

        vertex_task = transport.db_client.db.query(ProcessingTask).filter(
            ProcessingTask.task_type == TaskType.VERTEX_AI_BATCH_JOB.value,
            # Querying JSONB: task_metadata->>'batch_job_id' = batch_job_id_from_kg_task
            # Using .contains is for checking if a key exists or if a json blob is a sub-document.
            # For direct value check in JSONB, it's better to use specific operators if available,
            # or ensure the structure is consistent for .contains.
            # Assuming batch_job_id is consistently at the top level of task_metadata or result.
            # Let's prefer task_metadata as it's set on creation.
            # ProcessingTask.task_metadata['batch_job_id'] == batch_job_id_from_kg_task # This is not how SQLAlchemy queries JSONB
            ProcessingTask.task_metadata.has_key('batch_job_id'), # Ensure key exists
            ProcessingTask.task_metadata["batch_job_id"].astext == batch_job_id_from_kg_task
        ).order_by(ProcessingTask.created_at.desc()).first() # Take the latest if multiple (should ideally be unique)

        if vertex_task:
            logger.debug(f"Found Vertex AI task {vertex_task.id} with status {vertex_task.status}")
        else:
            logger.warning(f"No Vertex AI task found for batch_job_id: {batch_job_id_from_kg_task}")
        return vertex_task

    def _handle_vertex_job_completed(self, kg_task: ProcessingTask, vertex_task: ProcessingTask, transport: DataTransport) -> bool:
        """Handles the case where the Vertex AI batch job has completed successfully."""
        logger.info(f"Vertex AI job for KG task {kg_task.id} (Vertex Task {vertex_task.id}) completed. Updating KG task to BATCH_JOB_COMPLETED.")

        retrieved_results = vertex_task.result.get("processed_entity_results") if vertex_task.result else None

        kg_task_new_status = TaskStatus.BATCH_JOB_COMPLETED.value
        current_kg_result = kg_task.result or {}
        transport.update_task_status(
            task_id=kg_task.id,
            status=kg_task_new_status,
            result={
                **current_kg_result,
                "status": kg_task_new_status,
                "batch_job_completed_at": time.time(),
                "vertex_ai_task_id_for_results": str(vertex_task.id),
                "vertex_ai_task_status": vertex_task.status # Record the status of the vertex_task
            }
        )
        logger.info(f"KG task {kg_task.id} status updated to {kg_task_new_status}.")

        if retrieved_results:
            logger.info(f"Proceeding with knowledge graph building for KG task {kg_task.id} using prefetched results from Vertex task {vertex_task.id}.")
            self.continue_knowledge_graph_building(kg_task.id, processed_entity_results=retrieved_results)
        else:
            logger.warning(f"Vertex task {vertex_task.id} completed but 'processed_entity_results' are missing. Calling continue_knowledge_graph_building for KG task {kg_task.id} to attempt fallback.")
            self.continue_knowledge_graph_building(kg_task.id) # Fallback to fetching results in continue_knowledge_graph_building

        return True # Indicates the KG pipeline will continue

    def _handle_vertex_job_failed(self, kg_task: ProcessingTask, vertex_task: ProcessingTask, transport: DataTransport) -> None:
        """Handles the case where the Vertex AI batch job has failed."""
        logger.error(f"Vertex AI job for KG task {kg_task.id} (Vertex Task {vertex_task.id}) failed. Updating KG task to FAILED.")

        current_kg_result = kg_task.result or {}
        vertex_task_result = vertex_task.result or {}

        transport.update_task_status(
            task_id=kg_task.id,
            status=TaskStatus.FAILED.value,
            result={
                **current_kg_result,
                "status": TaskStatus.FAILED.value,
                "error": f"Associated Vertex AI job {vertex_task.id} failed. Vertex task error: {vertex_task_result.get('error', 'N/A')}",
                "vertex_ai_task_id_for_results": str(vertex_task.id),
                "vertex_ai_task_status": vertex_task.status,
                "failed_at": time.time()
            }
        )
        logger.error(f"KG task {kg_task.id} status updated to {TaskStatus.FAILED.value} due to Vertex AI job failure.")

    def _handle_vertex_job_still_processing(self, kg_task: ProcessingTask, vertex_task: ProcessingTask, transport: DataTransport) -> None:
        """Handles the case where the Vertex AI batch job is still processing."""
        logger.info(f"Vertex AI job for KG task {kg_task.id} (Vertex Task {vertex_task.id}) is still {vertex_task.status}. Updating KG task if necessary.")

        current_kg_result = kg_task.result or {}

        if kg_task.status == TaskStatus.BATCH_JOB_SUBMITTED.value or kg_task.status == "batch_job_created": # Include legacy
            new_kg_status = TaskStatus.BATCH_JOB_PROCESSING.value
            logger.info(f"Updating KG task {kg_task.id} from {kg_task.status} to {new_kg_status}.")
            transport.update_task_status(
                task_id=kg_task.id,
                status=new_kg_status,
                result={
                    **current_kg_result,
                    "status": new_kg_status,
                    "batch_job_processing_started_at": time.time(),
                    "last_checked_at": time.time(),
                    "vertex_ai_task_id_for_results": str(vertex_task.id),
                    "vertex_ai_task_status": vertex_task.status
                }
            )
        elif kg_task.status == TaskStatus.BATCH_JOB_PROCESSING.value or kg_task.status == "processing": # Include legacy
             logger.info(f"KG task {kg_task.id} is already {kg_task.status}. Updating last_checked_at.")
             transport.update_task_status(
                task_id=kg_task.id,
                status=kg_task.status, # Keep current status
                result={
                    **current_kg_result,
                    "status": kg_task.status,
                    "last_checked_at": time.time(),
                    "vertex_ai_task_id_for_results": str(vertex_task.id),
                    "vertex_ai_task_status": vertex_task.status
                }
            )
        else:
            logger.warning(f"KG task {kg_task.id} is in status {kg_task.status}, but Vertex AI task {vertex_task.id} is {vertex_task.status}. No specific action defined for this combination.")


    def _process_single_kg_task(self, kg_task: ProcessingTask, transport: DataTransport) -> bool:
        """Processes a single knowledge graph task by checking its associated Vertex AI job."""
        logger.debug(f"Processing single KG task: {kg_task.id}")

        kg_task_result = kg_task.result or {}
        batch_job_id_from_kg_task = kg_task_result.get("batch_job_id")

        if not batch_job_id_from_kg_task:
            logger.error(f"KG task {kg_task.id} has no batch_job_id in its result. Cannot process.")
            # Optionally update KG task to FAILED here
            # transport.update_task_status(kg_task.id, TaskStatus.FAILED.value, result={"error": "Missing batch_job_id in KG task result"})
            return False

        vertex_task = self._fetch_vertex_task(batch_job_id_from_kg_task, transport)

        if not vertex_task:
            logger.warning(f"No corresponding Vertex AI task found for batch_job_id {batch_job_id_from_kg_task} associated with KG task {kg_task.id}. Waiting for Vertex AI task to appear.")
            # It's possible the Vertex AI task hasn't been created or picked up by the DB yet.
            # We don't fail the KG task immediately; it will be retried in the next run.
            return False

        # Decision logic based on vertex_task.status
        if vertex_task.status == TaskStatus.COMPLETED.value:
            return self._handle_vertex_job_completed(kg_task, vertex_task, transport)
        elif vertex_task.status == TaskStatus.FAILED.value:
            self._handle_vertex_job_failed(kg_task, vertex_task, transport)
            return False # KG task is failed, not "completed" for counting purposes
        elif vertex_task.status in [TaskStatus.PROCESSING.value, TaskStatus.CREATED.value, TaskStatus.QUEUED.value]: # CREATED/QUEUED are also processing states for Vertex AI task
            self._handle_vertex_job_still_processing(kg_task, vertex_task, transport)
            return False
        else:
            logger.warning(f"Vertex AI task {vertex_task.id} has an unexpected status: {vertex_task.status}. Cannot determine action for KG task {kg_task.id}.")
            return False

    # --- End of Helper Methods ---

    # --- Start of Helper Methods ---

    def _get_actionable_kg_tasks(self, transport: DataTransport, limit: int) -> List[ProcessingTask]:
        """Queries for knowledge_graph_building tasks that are ready for status check."""
        logger.debug(f"Querying for actionable KG tasks (limit: {limit})")
        # Define target statuses including legacy ones for a transition period
        target_statuses = [
            TaskStatus.BATCH_JOB_SUBMITTED.value,
            TaskStatus.BATCH_JOB_PROCESSING.value,
            TaskStatus.BATCH_JOB_COMPLETED.value,  # Add completed status for continuation
            "processing",  # Legacy status
            "batch_job_created"  # Legacy status
        ]

        # Ensure that result is treated as JSONB for querying
        tasks = transport.db_client.db.query(ProcessingTask).filter(
            ProcessingTask.task_type == TaskType.KNOWLEDGE_GRAPH_BUILDING.value,
            ProcessingTask.status.in_(target_statuses),
            ProcessingTask.result.cast(JSONB).has_key('batch_job_id'),
            ProcessingTask.result.cast(JSONB)['async_processing'].astext == 'true' # Compare as text after casting
        ).order_by(ProcessingTask.updated_at.asc()).limit(limit).all()

        logger.debug(f"Found {len(tasks)} actionable KG tasks.")
        return tasks

    def _fetch_vertex_task(self, batch_job_id_from_kg_task: str, transport: DataTransport) -> Optional[ProcessingTask]:
        """Fetches the corresponding vertex_ai_batch_job task using task_metadata."""
        logger.debug(f"Fetching Vertex AI task for batch_job_id (Vertex AI Resource Name): {batch_job_id_from_kg_task}")

        vertex_task = transport.db_client.db.query(ProcessingTask).filter(
            ProcessingTask.task_type == TaskType.VERTEX_AI_BATCH_JOB.value,
            ProcessingTask.task_metadata.cast(JSONB)['batch_job_id'].astext == batch_job_id_from_kg_task
        ).order_by(ProcessingTask.created_at.desc()).first()

        if vertex_task:
            logger.debug(f"Found Vertex AI task {vertex_task.id} with status {vertex_task.status} for batch_job_id {batch_job_id_from_kg_task}")
        else:
            logger.warning(f"No Vertex AI task found with task_metadata['batch_job_id'] == {batch_job_id_from_kg_task}")
        return vertex_task

    def _handle_vertex_job_completed(self, kg_task: ProcessingTask, vertex_task: ProcessingTask, transport: DataTransport) -> bool:
        """Handles the case where the Vertex AI batch job has completed successfully."""
        logger.info(f"Vertex AI job for KG task {kg_task.id} (Vertex Task {vertex_task.id}) completed. Updating KG task to BATCH_JOB_COMPLETED.")

        retrieved_results = vertex_task.result.get("processed_entity_results") if vertex_task.result else None

        kg_task_new_status = TaskStatus.BATCH_JOB_COMPLETED.value
        current_kg_result = kg_task.result or {} # Ensure result is a dict
        transport.update_task_status(
            task_id=kg_task.id,
            status=kg_task_new_status,
            result={
                **current_kg_result,
                "status": kg_task_new_status,
                "batch_job_completed_at": time.time(),
                "vertex_ai_task_id_for_results": str(vertex_task.id),
                "vertex_ai_task_status": vertex_task.status
            }
        )
        logger.info(f"KG task {kg_task.id} status updated to {kg_task_new_status}.")

        if retrieved_results:
            logger.info(f"Proceeding with KG build for KG task {kg_task.id} using prefetched results from Vertex task {vertex_task.id}.")
            self.continue_knowledge_graph_building(kg_task.id, processed_entity_results=retrieved_results)
        else:
            logger.warning(f"Vertex task {vertex_task.id} completed but 'processed_entity_results' are missing. Calling continue_knowledge_graph_building for KG task {kg_task.id} to attempt fallback.")
            self.continue_knowledge_graph_building(kg_task.id)

        return True # Indicates the KG pipeline will continue for counting purposes

    def _handle_vertex_job_failed(self, kg_task: ProcessingTask, vertex_task: ProcessingTask, transport: DataTransport) -> None:
        """Handles the case where the Vertex AI batch job has failed."""
        logger.error(f"Vertex AI job for KG task {kg_task.id} (Vertex Task {vertex_task.id}) failed. Updating KG task to FAILED.")

        current_kg_result = kg_task.result or {}
        vertex_task_error_info = vertex_task.result.get('error', 'N/A') if vertex_task.result else 'N/A'

        transport.update_task_status(
            task_id=kg_task.id,
            status=TaskStatus.FAILED.value,
            result={
                **current_kg_result,
                "status": TaskStatus.FAILED.value,
                "error": f"Associated Vertex AI job {vertex_task.id} failed. Vertex task error: {vertex_task_error_info}",
                "vertex_ai_task_id_for_results": str(vertex_task.id),
                "vertex_ai_task_status": vertex_task.status,
                "failed_at": time.time()
            }
        )
        logger.error(f"KG task {kg_task.id} status updated to {TaskStatus.FAILED.value} due to Vertex AI job failure.")

    def _handle_vertex_job_still_processing(self, kg_task: ProcessingTask, vertex_task: ProcessingTask, transport: DataTransport) -> None:
        """Handles the case where the Vertex AI batch job is still processing."""
        logger.info(f"Vertex AI job for KG task {kg_task.id} (Vertex Task {vertex_task.id}) is still {vertex_task.status}. Updating KG task if necessary.")

        current_kg_result = kg_task.result or {}
        new_kg_status = kg_task.status
        update_result = {**current_kg_result} # Start with current result

        if kg_task.status == TaskStatus.BATCH_JOB_SUBMITTED.value or kg_task.status == "batch_job_created": # Include legacy
            new_kg_status = TaskStatus.BATCH_JOB_PROCESSING.value
            logger.info(f"Updating KG task {kg_task.id} from {kg_task.status} to {new_kg_status}.")
            update_result.update({
                "status": new_kg_status,
                "batch_job_processing_started_at": time.time(),
            })
        elif kg_task.status == TaskStatus.BATCH_JOB_PROCESSING.value or kg_task.status == "processing": # Include legacy
             logger.info(f"KG task {kg_task.id} is already {kg_task.status}. Updating last_checked_at.")
             update_result.update({
                "status": kg_task.status, # Keep status as is
            })
        else: # Should ideally not happen if _get_actionable_kg_tasks is correct
            logger.warning(f"KG task {kg_task.id} is in unexpected status {kg_task.status} while Vertex AI task {vertex_task.id} is {vertex_task.status}.")

        # Always update with the latest check info
        update_result.update({
            "last_checked_at": time.time(),
            "vertex_ai_task_id_for_results": str(vertex_task.id),
            "vertex_ai_task_status": vertex_task.status
        })

        transport.update_task_status(
            task_id=kg_task.id,
            status=new_kg_status,
            result=update_result
        )

    def _process_single_kg_task(self, kg_task: ProcessingTask, transport: DataTransport) -> bool:
        """Processes a single knowledge graph task by checking its associated Vertex AI job."""
        logger.debug(f"Processing single KG task: {kg_task.id}")

        kg_task_result = kg_task.result or {}
        batch_job_id_from_kg_task = kg_task_result.get("batch_job_id")

        if not batch_job_id_from_kg_task:
            logger.error(f"KG task {kg_task.id} has no batch_job_id in its result. Cannot process. Marking as failed.")
            current_kg_result = kg_task.result or {}
            transport.update_task_status(kg_task.id, TaskStatus.FAILED.value, result={**current_kg_result, "status": TaskStatus.FAILED.value, "error": "Missing batch_job_id in KG task result", "failed_at": time.time()})
            return False

        # Instead of looking for vertex_ai_batch_job tasks, directly check Vertex AI batch job status
        try:
            from transport.vertex_ai_batch_client import VertexAIBatchClient
            from google.cloud.aiplatform_v1.types import job_state as gca_job_state

            vertex_client = VertexAIBatchClient()
            job_state = vertex_client.get_batch_job_state(batch_job_id_from_kg_task)

            logger.info(f"Vertex AI batch job {batch_job_id_from_kg_task} state: {job_state}")

            # Update KG task status based on Vertex AI job state
            update_result = self._update_kg_task_status_based_on_job_state(kg_task, job_state, transport)
            if not update_result:
                return False

            # Also update the corresponding vertex_ai_batch_job task
            self._update_vertex_ai_batch_job_status(batch_job_id_from_kg_task, job_state, transport)

            # Check if task is already completed and needs to continue KG building
            if kg_task.status == TaskStatus.BATCH_JOB_COMPLETED.value:
                logger.info(f"KG task {kg_task.id} is already in batch_job_completed status. Continuing with KG building.")
                return self._continue_kg_building_after_batch_completion(kg_task.id, transport)

            # Check if Vertex AI job is completed
            if job_state == gca_job_state.JobState.JOB_STATE_SUCCEEDED:
                return self._handle_vertex_job_completed_direct(kg_task, batch_job_id_from_kg_task, transport)
            elif job_state in [gca_job_state.JobState.JOB_STATE_FAILED, gca_job_state.JobState.JOB_STATE_CANCELLED]:
                return self._handle_vertex_job_failed_direct(kg_task, batch_job_id_from_kg_task, job_state, transport)
            else:
                logger.info(f"Vertex AI batch job {batch_job_id_from_kg_task} is still running (state: {job_state}). KG task {kg_task.id} will be checked again in next cycle.")
                return False

        except Exception as e:
            logger.error(f"Error checking Vertex AI batch job status for {batch_job_id_from_kg_task}: {e}")
            # Don't fail the task immediately, retry in next cycle
            return False

    def _update_kg_task_status_based_on_job_state(self, kg_task: ProcessingTask, job_state, transport: DataTransport) -> bool:
        """Update KG task status based on Vertex AI job state."""
        from google.cloud.aiplatform_v1.types import job_state as gca_job_state

        current_kg_result = kg_task.result or {}
        update_result = {**current_kg_result}

        if job_state == gca_job_state.JobState.JOB_STATE_SUCCEEDED:
            new_status = TaskStatus.BATCH_JOB_COMPLETED.value
            update_result.update({
                "status": new_status,
                "batch_job_completed_at": time.time(),
            })
        elif job_state in [gca_job_state.JobState.JOB_STATE_FAILED, gca_job_state.JobState.JOB_STATE_CANCELLED]:
            new_status = TaskStatus.FAILED.value
            update_result.update({
                "status": new_status,
                "error": f"Vertex AI batch job failed with state: {job_state}",
                "failed_at": time.time(),
            })
        elif job_state in [gca_job_state.JobState.JOB_STATE_RUNNING, gca_job_state.JobState.JOB_STATE_PENDING]:
            new_status = TaskStatus.BATCH_JOB_PROCESSING.value
            update_result.update({
                "status": new_status,
                "batch_job_processing_started_at": time.time(),
            })
        else:
            # Unknown state, keep current status but update last checked
            new_status = kg_task.status
            update_result.update({
                "last_checked_at": time.time(),
            })

        try:
            transport.update_task_status(
                task_id=kg_task.id,
                status=new_status,
                result=update_result
            )
            logger.info(f"Updated KG task {kg_task.id} status to {new_status}")
            return True
        except Exception as e:
            logger.error(f"Error updating KG task {kg_task.id} status: {e}")
            return False

    def _handle_vertex_job_completed_direct(self, kg_task: ProcessingTask, batch_job_id: str, transport: DataTransport) -> bool:
        """Handle completed Vertex AI batch job by downloading results and continuing KG building."""
        logger.info(f"Vertex AI batch job {batch_job_id} completed. Processing results for KG task {kg_task.id}")

        try:
            from transport.vertex_ai_batch_client import VertexAIBatchClient

            vertex_client = VertexAIBatchClient()

            # Download batch prediction results
            results = vertex_client.download_batch_prediction_results(batch_job_id)

            if results.get("status") == "success":
                logger.info(f"Successfully downloaded batch results for job {batch_job_id}")

                # Update task with results and continue to KG building
                current_kg_result = kg_task.result or {}
                update_result = {
                    **current_kg_result,
                    "status": TaskStatus.BATCH_JOB_COMPLETED.value,
                    "batch_job_completed_at": time.time(),
                    "batch_results": results,
                    "entities": results.get("entities", []),
                    "relationships": results.get("relationships", [])
                }

                transport.update_task_status(
                    task_id=kg_task.id,
                    status=TaskStatus.BATCH_JOB_COMPLETED.value,
                    result=update_result
                )

                # Continue with knowledge graph building
                return self._continue_kg_building_after_batch_completion(kg_task.id, transport)

            else:
                logger.error(f"Failed to download batch results for job {batch_job_id}: {results.get('message', 'Unknown error')}")

                # Mark task as failed
                current_kg_result = kg_task.result or {}
                transport.update_task_status(
                    task_id=kg_task.id,
                    status=TaskStatus.FAILED.value,
                    result={
                        **current_kg_result,
                        "status": TaskStatus.FAILED.value,
                        "error": f"Failed to download batch results: {results.get('message', 'Unknown error')}",
                        "failed_at": time.time()
                    }
                )
                return False

        except Exception as e:
            logger.error(f"Error handling completed Vertex AI job {batch_job_id}: {e}")

            # Mark task as failed
            current_kg_result = kg_task.result or {}
            transport.update_task_status(
                task_id=kg_task.id,
                status=TaskStatus.FAILED.value,
                result={
                    **current_kg_result,
                    "status": TaskStatus.FAILED.value,
                    "error": f"Error handling completed batch job: {str(e)}",
                    "failed_at": time.time()
                }
            )
            return False

    def _handle_vertex_job_failed_direct(self, kg_task: ProcessingTask, batch_job_id: str, job_state, transport: DataTransport) -> bool:
        """Handle failed Vertex AI batch job."""
        logger.error(f"Vertex AI batch job {batch_job_id} failed with state: {job_state}")

        # Mark KG task as failed
        current_kg_result = kg_task.result or {}
        transport.update_task_status(
            task_id=kg_task.id,
            status=TaskStatus.FAILED.value,
            result={
                **current_kg_result,
                "status": TaskStatus.FAILED.value,
                "error": f"Vertex AI batch job failed with state: {job_state}",
                "failed_at": time.time()
            }
        )
        return False

    def _update_vertex_ai_batch_job_status(self, batch_job_id: str, job_state, transport: DataTransport) -> bool:
        """Update the corresponding vertex_ai_batch_job task status based on Vertex AI job state."""
        from google.cloud.aiplatform_v1.types import job_state as gca_job_state
        from common.task_definitions import TaskType

        try:
            # Find the vertex_ai_batch_job task with this batch_job_id
            vertex_ai_tasks = transport.db_client.db.query(ProcessingTask).filter(
                ProcessingTask.task_type == TaskType.VERTEX_AI_BATCH_JOB.value,
                ProcessingTask.result.op('->>')('batch_job_id') == batch_job_id
            ).all()

            if not vertex_ai_tasks:
                logger.warning(f"No vertex_ai_batch_job task found for batch_job_id: {batch_job_id}")
                return False

            for vertex_task in vertex_ai_tasks:
                logger.info(f"Updating vertex_ai_batch_job task {vertex_task.id} for batch job {batch_job_id}")

                current_result = vertex_task.result or {}
                update_result = {**current_result}

                if job_state == gca_job_state.JobState.JOB_STATE_SUCCEEDED:
                    new_status = TaskStatus.COMPLETED.value
                    update_result.update({
                        "status": TaskStatus.COMPLETED.value,
                        "completed_at": time.time(),
                        "vertex_ai_job_state": "JOB_STATE_SUCCEEDED"
                    })
                elif job_state in [gca_job_state.JobState.JOB_STATE_FAILED, gca_job_state.JobState.JOB_STATE_CANCELLED]:
                    new_status = TaskStatus.FAILED.value
                    update_result.update({
                        "status": TaskStatus.FAILED.value,
                        "error": f"Vertex AI batch job failed with state: {job_state}",
                        "failed_at": time.time(),
                        "vertex_ai_job_state": str(job_state)
                    })
                elif job_state in [gca_job_state.JobState.JOB_STATE_RUNNING, gca_job_state.JobState.JOB_STATE_PENDING]:
                    new_status = TaskStatus.PROCESSING.value
                    update_result.update({
                        "status": TaskStatus.PROCESSING.value,
                        "processing_started_at": time.time(),
                        "vertex_ai_job_state": str(job_state)
                    })
                else:
                    # Unknown state, keep current status but update last checked
                    new_status = vertex_task.status
                    update_result.update({
                        "last_checked_at": time.time(),
                        "vertex_ai_job_state": str(job_state)
                    })

                transport.update_task_status(
                    task_id=vertex_task.id,
                    status=new_status,
                    result=update_result
                )
                logger.info(f"Updated vertex_ai_batch_job task {vertex_task.id} status to {new_status}")

            return True

        except Exception as e:
            logger.error(f"Error updating vertex_ai_batch_job task for batch_job_id {batch_job_id}: {e}")
            return False

    def _continue_kg_building_after_batch_completion(self, kg_task_id: uuid.UUID, transport: DataTransport) -> bool:
        """Continue knowledge graph building after batch completion."""
        try:
            logger.info(f"Continuing KG building for task {kg_task_id}")

            # Use the existing continue_knowledge_graph_building method
            result = self.continue_knowledge_graph_building(kg_task_id)

            if result.get("status") in ["success", "completed"]:
                logger.info(f"Successfully continued KG building for task {kg_task_id}")
                return True
            else:
                logger.error(f"Failed to continue KG building for task {kg_task_id}: {result.get('message', 'Unknown error')}")
                return False

        except Exception as e:
            logger.error(f"Error continuing KG building for task {kg_task_id}: {e}")
            return False

    # --- End of Helper Methods ---

    def check_and_continue_batch_jobs(self) -> Dict[str, Any]:
        logger.info("Running periodic check for knowledge graph batch jobs")
        processed_count = 0
        continued_to_kg_build_count = 0
        MAX_TASKS_PER_RUN = 10

        with DataTransport() as transport:
            actionable_kg_tasks = self._get_actionable_kg_tasks(transport, MAX_TASKS_PER_RUN)

            if not actionable_kg_tasks:
                logger.info("No actionable knowledge graph batch jobs found to check.")
                return {"status": TaskStatus.COMPLETED.value, "processed_tasks_count": 0, "continued_to_kg_build_count": 0, "message": "No actionable tasks."}

            logger.info(f"Found {len(actionable_kg_tasks)} KG tasks with batch jobs to check.")

            for kg_task in actionable_kg_tasks:
                logger.info(f"Processing KG task ID: {kg_task.id}, current status: {kg_task.status}, batch_job_id: {kg_task.result.get('batch_job_id') if kg_task.result else 'N/A'}")
                try:
                    if self._process_single_kg_task(kg_task, transport):
                        continued_to_kg_build_count += 1
                    processed_count += 1
                except Exception as e:
                    logger.error(f"Critical error processing KG task {kg_task.id} in check_and_continue_batch_jobs: {e}", exc_info=True)
                    try:
                        current_kg_result = kg_task.result or {}
                        transport.update_task_status(kg_task.id, TaskStatus.FAILED.value, result={**current_kg_result, "status": TaskStatus.FAILED.value, "error": f"Outer error in check_and_continue_batch_jobs: {str(e)}", "failed_at": time.time()})
                    except Exception as ie:
                         logger.error(f"Further error updating task {kg_task.id} to failed after outer error: {ie}")


        logger.info(f"Finished periodic check. Processed KG tasks: {processed_count}, Continued to KG build: {continued_to_kg_build_count}")
        return {
            "status": TaskStatus.COMPLETED.value,
            "processed_tasks_count": processed_count,
            "continued_to_kg_build_count": continued_to_kg_build_count,
            "message": f"Processed {processed_count} tasks, continued {continued_to_kg_build_count} to KG build."
        }
