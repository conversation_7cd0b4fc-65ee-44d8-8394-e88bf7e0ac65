#!/usr/bin/env python3
"""
Simple system test runner for comprehensive document ingestion testing.

This script runs the comprehensive system tests directly without pytest.
It's useful for quick testing and debugging.

Usage:
    python test_system.py
"""

import os
import sys
import time
import logging
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """Main function to run system tests."""
    logger.info("🚀 Starting Simple System Test Runner")
    logger.info("=" * 60)
    
    try:
        # Import the test class
        from tests.system.test_comprehensive_system import TestComprehensiveSystem
        
        # Create test instance
        test_instance = TestComprehensiveSystem()
        
        # Run setup
        logger.info("Setting up test environment...")
        TestComprehensiveSystem.setup_class()
        
        # Run tests in order
        tests = [
            ("API Health Check", test_instance.test_01_api_health_check),
            ("PubMed Article Ingestion", test_instance.test_02_ingest_pubmed_articles),
            ("Sample File Ingestion", test_instance.test_03_ingest_sample_files),
            ("Monitor Processing Completion", test_instance.test_04_monitor_processing_completion),
            ("Verify RAG Chunks", test_instance.test_05_verify_rag_chunks),
            ("Verify KG Chunks", test_instance.test_06_verify_kg_chunks),
            ("Verify Entities Extracted", test_instance.test_07_verify_entities_extracted),
            ("Verify Neo4j Data", test_instance.test_08_verify_neo4j_data),
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*20} {test_name} {'='*20}")
            try:
                test_func()
                logger.info(f"✅ {test_name} PASSED")
                passed += 1
            except Exception as e:
                logger.error(f"❌ {test_name} FAILED: {e}")
                failed += 1
                # Continue with other tests
        
        # Summary
        logger.info("\n" + "=" * 60)
        logger.info("TEST SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Total tests: {len(tests)}")
        logger.info(f"Passed: {passed}")
        logger.info(f"Failed: {failed}")
        
        if failed == 0:
            logger.info("🎉 All tests passed!")
            return 0
        else:
            logger.error(f"❌ {failed} test(s) failed")
            return 1
            
    except KeyboardInterrupt:
        logger.info("⚠️  Tests interrupted by user")
        return 130
    except Exception as e:
        logger.error(f"❌ Error running tests: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
